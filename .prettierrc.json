{"generatorStarSpacing": true, "offsetTernaryExpressions": true, "spaceBeforeFunctionParen": true, "yieldStarSpacing": true, "arrowParens": "always", "bracketSameLine": true, "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "ignore", "insertPragma": false, "jsxSingleQuote": true, "jsxBracketSameLine": true, "printWidth": 120, "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false, "tabs": false, "noBracketSpacing": false, "vueIndentScriptAndStyle": true, "rangeStart": 0, "endOfLine": "lf", "max-lines-per-function": [2, {"max": 320, "skipComments": true, "skipBlankLines": true}], "editor.detectIndentation": false, "stylusSupremacy.insertSpaces": false, "stylusSupremacy.tabSize": 4, "[stylus]": {"editor.defaultFormatter": "manta.stylus-supremacy"}}