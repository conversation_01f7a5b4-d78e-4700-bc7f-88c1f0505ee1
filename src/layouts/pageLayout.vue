<script setup>
  const props = defineProps({
    customClass: {
      type: String,
      default: '',
    },
  });
</script>

<template>
  <div :class="['page-container', props.customClass]">
    <div class="page-header">
      <slot name="header"></slot>
    </div>
    <div class="page-content">
      <slot name="container"></slot>
    </div>
    <div class="page-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<style scoped lang="stylus">
  .page-container
    height 100%
    display flex
    flex-direction column
  .page-content
    flex 1
    height 100%
    overflow auto
</style>
