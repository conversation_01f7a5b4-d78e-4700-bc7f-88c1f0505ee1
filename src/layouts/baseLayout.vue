<script setup>
  import { onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  const route = useRoute();
  onMounted(() => {
    // 监听路由变化，打印当前路由信息
    console.log('当前路由:', route.path);
  });
</script>

<template>
  <div class="base_layout_container">
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
</template>

<style lang="stylus" scoped>
  .base_layout_container
    height 100%
</style>
