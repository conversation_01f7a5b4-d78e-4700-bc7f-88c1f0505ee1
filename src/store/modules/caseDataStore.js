// import {sceneProductDemoList, selectSceneList, spaceList} from '@/views/designTool/components/intelligentScene/intelligentSceneConf'
import { defineStore } from 'pinia';

export const useCaseDataStore = defineStore('caseDataStore', {
  state: () => ({
    viewScale: 0.2, // 画面缩放比例
    sceneSpaceList: [], // 智能场景空间列表
    sceneDeviceList: [], // 智能场景空间下商品列表
    selectSceneList: [], // 智能场景空间下选中的场景列表
    statusMap: {
      hasFloorImage: false, // 是否有底图  是否有完整闭合的空间
    }, // 各种状态的集合
    deviceSystemIds: [], // 已绘制>1个封闭房间和在封闭房间内放置>1个商品 的系统列表
    renderingImgList: [], // 渲染图列表
  }),
  actions: {
    /**
     * @description 设置方案状态
     * @param status
     */
    setStatusMap(status) {
      this.statusMap = status;
    },
    setViewScale(scale) {
      this.viewScale = scale;
    },
    setSpaceList(res) {
      // this.sceneSpaceList = spaceList;
      this.sceneSpaceList = res.data;
    },
    setDevicesList(res) {
      // this.sceneDeviceList = sceneProductDemoList;
      this.sceneDeviceList = res.data;
    },
    setSelectSceneList(res) {
      // this.selectSceneList = selectSceneList;
      this.selectSceneList = res.data;
    },
    setDeviceSystemIds(ids) {
      this.deviceSystemIds = ids;
    },
    /**
     * @description 添加渲染图数据
     * @param {*} data 渲染图数据
     * @returns {boolean} 添加成功返回 true，失败返回 false
     */
    setRenderingImg(data) {
      if (data) {
        this.renderingImgList.push(data);
        return true;
      }
      return false;
    },
  },
});
