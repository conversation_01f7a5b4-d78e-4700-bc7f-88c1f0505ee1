import store from '@/store';
import { defineStore } from 'pinia';
import { toRaw } from 'vue';
import { _API_getDict, _API_getHouseType, _API_getSystemList } from '@/api/comm.api';
import { LOCAL_STORAGE_PREFIX_KEY } from '@/config/const';
import { isArray } from '@/utils/isType';
import { HOUSE_TYPE_LIST, SYSTEM_LIST } from '@/config/dictKeys';

const REQUEST_API_KEYS = [SYSTEM_LIST, HOUSE_TYPE_LIST];

export const useDictStore = defineStore('appDict', {
  state: () => ({
    [SYSTEM_LIST]: [],
    [HOUSE_TYPE_LIST]: [], // 房间类型
  }),
  actions: {
    setSystemList() {
      return new Promise((resolve, reject) => {
        if (this.$state[[SYSTEM_LIST]] && this.$state[[SYSTEM_LIST]].length) {
          processMap(this.$state[[SYSTEM_LIST]], SYSTEM_LIST, this);
          resolve(toRaw(this.$state[SYSTEM_LIST]));
        }
        _API_getSystemList()
          .then((res) => {
            if (res?.code === '0' && isArray(res?.data)) {
              this[SYSTEM_LIST] = res.data.map((item) => ({
                label: item.systemName,
                value: item.systemId,
              }));
              processMap(res.data, SYSTEM_LIST, this, { name: 'label', code: 'value' });
              resolve(toRaw(this[SYSTEM_LIST]));
            } else {
              reject();
            }
          })
          .catch((error) => {
            reject();
            console.error('获取系统列表失败:', error);
          });
      });
    },
    setHouseTypeList() {
      return new Promise((resolve, reject) => {
        if (this.$state[[HOUSE_TYPE_LIST]] && this.$state[[HOUSE_TYPE_LIST]].length) {
          processMap(this.$state[[HOUSE_TYPE_LIST]], HOUSE_TYPE_LIST, this, { name: 'label', code: 'value' });
          resolve(toRaw(this.$state[HOUSE_TYPE_LIST]));
        }
        _API_getHouseType()
          .then((res) => {
            if (res?.code === '0' && isArray(res?.data)) {
              this[HOUSE_TYPE_LIST] = res.data.map((item) => ({
                label: item.spaceName,
                value: item.spaceId,
              }));
              processMap(res.data, HOUSE_TYPE_LIST, this, { name: 'spaceName', code: 'spaceId' });
              resolve(toRaw(this[HOUSE_TYPE_LIST]));
            } else {
              reject();
            }
          })
          .catch((error) => {
            reject();
            console.error('获取空间列表失败:', error);
          });
      });
    },
    getDictStore: async function (key) {
      if (REQUEST_API_KEYS.includes(key)) {
        let keyName = key.charAt(0).toUpperCase() + key.slice(1);
        return this[`set` + keyName]();
      }
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        if (this.$state[[key]] && this.$state[[key]].length) {
          processMap(this.$state[[key]], key, this);
          resolve(toRaw(this.$state[key]));
        } else {
          try {
            const res = await _API_getDict(key);
            if (res?.status === '0' && res?.data) {
              this.$state[[key]] = res.data;
              processMap(this.$state[[key]], key, this);
              resolve(toRaw(this.$state[key])); // 防止数据被修改
            } else {
              reject(res);
            }
          } catch (error) {
            reject(error);
          }
        }
      });
    },
    /**
     * @description 获取字典map
     * @param {String} key 字典值key
     * @returns {Promise<unknown>}  {0: '精装', 1: '局改'}
     */
    getDictMap: function (key) {
      return new Promise((resolve, reject) => {
        if (this.$state[[key + '_c_map']]) {
          resolve(this.$state[[key + '_c_map']]);
        }
        if (this.$state[[key]] && this.$state[[key]].length) {
          let map = processMap(this.$state[[key]], key, this);
          resolve(toRaw(map));
        } else {
          this.getDictStore(key)
            .then((res) => {
              let map = processMap(res, key, this);
              resolve(toRaw(map));
            })
            .catch((error) => {
              reject(error);
            });
        }
      });
    },
  },
  persist: {
    enabled: true,
    key: `${LOCAL_STORAGE_PREFIX_KEY}_appDict`,
    storage: sessionStorage,
  },
});

/**
 * @description 处理字典
 * @param {Array} res 字典值
 * @param {String} key 字典值key
 * @param {Object} storeInstance store实例
 * @param field
 * @returns {Object}
 */
function processMap(res, key, storeInstance, field = { name: 'name', code: 'code' }) {
  let map = res.reduce((acc, cur) => {
    acc[cur[field.code]] = cur[field.name];
    return acc;
  }, {});
  storeInstance.$state[[key + '_c_map']] = map;
  return map;
}

export function useDictStoreHook() {
  return useDictStore(store);
}
