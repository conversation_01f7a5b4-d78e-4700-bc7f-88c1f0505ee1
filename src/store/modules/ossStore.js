import { defineStore } from 'pinia';
import store from '@/store';
import { formatDateString } from '@/utils/tools';
import { getOSSToken } from '@/api/comm.api';

export const useOssStore = defineStore('useOssStore', {
  state: () => ({
    ossInfo: {},
  }),
  actions: {
    setOssInfo() {
      this.ossInfo = {};
    },
    getOSSInfo() {
      let self = this;
      return new Promise((resolve, reject) => {
        if (!Object.keys(self.ossInfo).length) {
          getToken()
            .then((res) => {
              this.setOssInfo(res);
              resolve(res);
            })
            .catch((error) => {
              reject(error);
            });
          return;
        }
        let dateStr = formatDateString(self.ossInfo.expiration);
        let isExpired = new Date(dateStr).getTime() - new Date().getTime();
        if (isExpired < 0) {
          getToken()
            .then((res) => {
              this.setOssInfo(res);
              resolve(res);
            })
            .catch((error) => {
              reject(error);
            });
        } else {
          resolve(self.ossInfo);
        }
      });
    },
  },
});

async function getToken() {
  return new Promise((resolve, reject) => {
    getOSSToken()
      .then((res) => {
        if (res?.status === '0') {
          resolve({
            ...res.data,
            regionId: 'cn-qingdao',
          });
        } else {
          reject(res);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export function useOssStoreuseCachedViewStoreHook() {
  return useOssStore(store);
}
