import { defineStore } from 'pinia';
export const SAVE_CASE_STATE = 'saveCaseState'; // 保存方案
export const CREATE_CASE_STATE = 'createCaseState'; // 创建方案
export const IMPORT_BASE_MAP = 'importBaseMap'; // 导入底图
export const CREATE_PROPOSAL_STATE = 'createProposalState'; // 创建提案
export const CREATE_RENDER_WORKING_STATE = 'createRenderWorkingState'; // 创建渲染施工图
export const SET_INTELLIGENT_SCENE_STATE = 'setIntelligentScene'; // 设置智慧场景
export const CREATE_QUOTATION_STATE = 'createQuotationState'; // 创建报价单
export const RECOGNISE_DIALOG_STATE = 'recognise_dialog_state'; // 户型识别
export const CREATE_WORKING_RESULT_STATE = 'createWorkingResultState'; // 施工图成果

const ALL_STATE = [
  SAVE_CASE_STATE,
  CREATE_CASE_STATE,
  IMPORT_BASE_MAP,
  CREATE_PROPOSAL_STATE,
  CREATE_RENDER_WORKING_STATE,
  SET_INTELLIGENT_SCENE_STATE,
  CREATE_QUOTATION_STATE,
  RECOGNISE_DIALOG_STATE,
  CREATE_WORKING_RESULT_STATE
];

let stateObj = {};
ALL_STATE.map((item) => {
  stateObj[item] = {
    show: false,
    props: {},
    event: noop,
  };
});

function noop() {}
export const useGlobalModalStore = defineStore('globalModalStore', {
  state: () => ({
    ...stateObj,
  }),
  actions: {
    setStoreState(key, state) {
      this.$state[key].show = true;
      for (const stateKey in state) {
        if (stateKey === 'event') {
          this.$state[key][stateKey] = state[stateKey] || noop;
        } else {
          this.$state[key][stateKey] = state[stateKey];
        }
      }
    },
    clearStoreState(key) {
      this.$state[key].show = false;
      this.$state[key].props = {};
      this.$state[key].event = noop;
    },
  },
});
