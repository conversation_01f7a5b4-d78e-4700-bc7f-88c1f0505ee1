<script setup>
  import zhC<PERSON> from 'ant-design-vue/es/locale/zh_CN';
  import 'dayjs/locale/zh-cn';
  import { _API_getUserInfo } from '@/api/userInfo';
  import { onMounted, onUnmounted } from 'vue';
  import { useUserStore } from '@/store/modules/userStore';
  import NetworkDetector from '@/components/networkDetector';
  import { MAIN_PLATFORM_STORAGE_KEY } from '@/config/const';
  import { useModal } from '@/hooks/useModal';
  const local = zhCN;
  const userStore = useUserStore();

  /**
   * @description 获取用户信息
   * @returns {Promise<void>}
   */
  async function getUserInfo() {
    _API_getUserInfo().then((res) => {
      if (res.status === '0') {
        userStore.setZjsjUserInfo(res.data);
      }
    });
  }

  /**
   * @description 监听 storage 变化
   */
  function handleStorageChange(e) {
    if (e.key === MAIN_PLATFORM_STORAGE_KEY) {
      let oldValue = JSON.parse(e.oldValue || '{}');
      let newValue = JSON.parse(e.newValue || '{}');
      let newAccountNo = newValue?.accountId;
      let oldAccountNo = oldValue?.accountId;
      let oldName = oldValue?.accountDTO?.accountName;
      let newName = newValue?.accountDTO?.accountName;
      if (oldAccountNo && newAccountNo && oldAccountNo !== newAccountNo) {
        useModal({
          title: '用户更换',
          content: `'检测到用户已由${oldName}切换为${newName}'`,
          closeable: false,
          hideCancel: true,
          onOk: () => {
            window.close();
          },
        });
      }
    }
  }

  onMounted(async () => {
    window.addEventListener('storage', handleStorageChange);

    await getUserInfo();
  });

  onUnmounted(() => {
    window.removeEventListener('storage', handleStorageChange);
  });
</script>

<template>
  <a-config-provider :locale="local">
    <router-view />
    <!-- 全局网络检测组件 -->
    <NetworkDetector />
  </a-config-provider>
</template>

<style></style>
