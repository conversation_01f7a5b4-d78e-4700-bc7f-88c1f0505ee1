import { http } from '@/utils/http/request';

/**
 * @description 根据pid获取地址信息
 * @param pid
 * @returns {Promise<never>|Promise<axios.AxiosResponse<any>>}
 * @private
 */
export function _API_getAddressByPid(pid) {
  if (!pid) {
    return Promise.reject(new Error('Parameter "pid" is required'));
  }
  return http({
    method: 'get',
    url: `/area/uoc?superior=${pid}`,
  });
}

// 获取省市区的全部数据的树结构
export function _API_getFullArea(params) {
  return http.get(`/area/full`, { params });
}

/**
 * @description 获取OSS上传token
 * @returns {Promise<AxiosResponse<any>>}
 */
export function getOSSToken() {
  return http.get('/api/designerReviewRecord/getDesignVideoUploadToken');
}

/**
 * @description 获取字典值
 * @param key
 */
export function _API_getDict(key) {
  return http({
    method: 'get',
    url: `/api/data_dict/${key}`,
  });
}

/**
 * @description 企业库一级类目
 */
export function _API_getSystemList() {
  return http({
    method: 'get',
    url: `/api/server/zjsj/v3/zk/system/list`,
  });
}

/**
 * @description 上传文件到OSS
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function uploadFile(data) {
  return http({
    url: '/api/file_mgr/oss/upload',
    method: 'post',
    data: data,
  });
}

/**
 * @description 对参数进行加密
 * @param params
 * @returns {Promise<axios.AxiosResponse<any>>}
 * @private
 */
export function _API_setCode(params) {
  return http({
    method: 'post',
    url: '/api/server/zjsj/v3/param/encode',
    data: {
      value: JSON.stringify(params),
    },
  });
}

/**
 * @description 根据code获取加密的内容
 * @param code
 * @returns {Promise<axios.AxiosResponse<any>>}
 * @private
 */
export function _api_getCode(code) {
  return http({
    method: 'get',
    url: `/api/server/zjsj/v3/param/decode/${code}`,
  });
}

/**
 * @description 搜索户型图
 * @param params
 * @returns {Promise<axios.AxiosResponse<any>>}
 * @private
 */
export function _API_searchHouse(params) {
  return http({
    method: 'get',
    url: '/api/server/zjsj/v3/zk/floorPlan/search',
    data: params,
  });
}

/**
 * @description 获取房间类型
 * @returns {Promise<axios.AxiosResponse<any>>}
 * @private
 */
export function _API_getHouseType() {
  return http({
    method: 'get',
    url: '/api/server/zjsj/v3/zk/space/list',
  });
}
