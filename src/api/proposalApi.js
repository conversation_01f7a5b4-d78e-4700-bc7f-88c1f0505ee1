import { http } from '@/utils/http/zjHttp/request';
import { http as zkHttp } from '@/utils/http/request';

export function getAuthCode() {
  return http({
    method: 'get',
    url: '/api-gw/contentPlatformPub/material/dzpt/oauth2/code',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  });
}

/**
 * @description 提案书信息保存接口
 * @param code
 * @param version
 * @param params
 * @returns {*}
 * @private
 */
export function _API_saveProposalData(code, version, params) {
  return zkHttp({
    method: 'post',
    url: `/api/server/zjsj/v3/zk/proposalHouseTypeImg/save?code=${code}&version=${version}`,
    data: params,
  });
}
