import { http } from '@/utils/http/request';

/**
 * 获取场景列表
 *
 * @param data 需要传递的数据
 * @returns 返回请求结果
 */
export function _API_getListByMk(data) {
  return http({
    url: '/api/server/zjsj/v3/zk/scene/list',
    method: 'post',
    data,
  });
}
/**
 * 根据查询条件获取商品列表
 *
 * @param {Object} data 请求参数
 * @returns {Promise<Object>} 返回的 Promise 对象，包含组件列表数据
 */
export function _API_getComponentList(data) {
  return http({
    url: '/api/server/zjsj/v3/zk/commonent/search',
    method: 'post',
    data,
  });
}

/**
 * 获取系统列表、子模块列表、商品列表
 *
 * @returns {Promise<Object>} - 返回一个包含模块详情的对象
 */

export function _API_getSystemSybModuleAndProduct() {
  return http({
    url: '/api/server/zjsj/v3/zk/model/detail',
    method: 'get',
  });
}

export function _API_getSceneListByRoomId(data) {
  return http({
    url: '/api/server/zjsj/v3/zk/scenes/list',
    method: 'post',
    data,
  });
}
