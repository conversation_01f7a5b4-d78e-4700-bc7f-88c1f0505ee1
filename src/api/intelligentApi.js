import { http } from '@/utils/http/request';

export function _API_getIntelligentData() {
  return http({
    url: '/api/server/zjsj/v3/zk/model/detail',
    method: 'get',
  });
}

export function _API_saveIntelligentData(data) {
  return http({
    url: '/api/server/zjsj/v3/zk/case/save',
    method: 'post',
    data,
  });
}

// /api/server/zjsj/v3/zk/caseImage/save?code=&version=&type=
export function _API_saveIntelligentImage(data, params) {
  return http({
    url: '/api/server/zjsj/v3/zk/caseImage/save',
    method: 'post',
    params,
    data,
  });
}

/**
 * @description 查询智控方案信息
 * @param data
 * @returns {Promise<axios.AxiosResponse<any>>}
 * @private
 */
export function _API_getIntelligentCase(data) {
  return http({
    url: `/api/server/zjsj/v3/zk/case`,
    method: 'get',
    data,
  });
}

/**
 * @description 报价单预览
 * @params caseCode 智控方案编码
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function _API_getQuotePreview(caseCode) {
  return http({
    url: '/api/server/zjsj/v3/zk/quote/preview',
    method: 'post',
    params: { caseCode },
  });
}


/**
 * @description 保存报价清单
 * @params caseCode 智控方案编码
 * @params designerShopCode 设计师门店编码
 * @params designerShopName 设计师门店名称
 * @returns {Promise<axios.AxiosResponse<any>>}
 */
export function _API_quoteSave(data) {
  return http({
    url: '/api/server/zjsj/v3/zk/quote/save',
    method: 'post',
    data,
  });
}
