import { Modal } from 'ant-design-vue';

/**
 * example AntDesign的所有参数都支持 额外扩展了两个参数
 * hideCancel 隐藏取消按钮
 * hideConfirm 隐藏确认按钮
 */

/**
 * 默认配置
 */
const defaultConfig = {
  title: '提示',
  cancelText: '取消',
  keyboard: false,
  mask: true,
  maskClosable: false,
  okText: '确定',
};

/**
 * 创建Modal实例
 * @param {string} type - Modal类型 (info|success|error|warning|confirm)
 * @param {Object} options - Modal配置选项
 * @param {boolean} options.hideCancel - 隐藏取消按钮
 * @param {boolean} options.hideConfirm - 隐藏确定按钮
 * @returns {Object} Modal实例
 */
function createModal(type = 'warning', options = {}) {
  const { hideCancel, hideConfirm, ...restOptions } = options;

  const config = {
    ...defaultConfig,
    ...restOptions,
  };

  // 处理隐藏按钮
  if (hideCancel) {
    config.cancelButtonProps = { style: { display: 'none' } };
  }

  if (hideConfirm) {
    config.okButtonProps = { style: { display: 'none' } };
  }

  switch (type) {
    case 'info':
      return Modal.info(config);
    case 'success':
      return Modal.success(config);
    case 'error':
      return Modal.error(config);
    case 'warning':
      return Modal.warning(config);
    case 'confirm':
      return Modal.confirm(config);
    default:
      return Modal.warning(config);
  }
}

/**
 * useModal Hook
 * @param {Object} options - 配置选项
 * @param {string} options.type - Modal类型 (info, success, error, warning, confirm)
 * @param {...Object} config - 其他配置选项
 * @returns {Object} Modal实例
 */
export function useModal({ type = 'warning', ...config } = {}) {
  return createModal(type, {
    title: '提示',
    ...config,
  });
}

/**
 * 全局Modal对象
 */ export const globalModal = {
  createModal,
};

export default {
  useModal,
  globalModal,
  createModal,
};

// 单独导出createModal函数
export { createModal };
