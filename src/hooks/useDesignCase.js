// import { ref } from 'vue';
import { _API_saveIntelligentData, _API_saveIntelligentImage } from '@/api/intelligentApi';
import { message } from 'ant-design-vue';
import { useUserStore } from '@/store/modules/userStore';
import { useDictStore } from '@/store/modules/dictStore';
import { useCaseDataStore } from '@/store/modules/caseDataStore';
import { handleCreateProposalImg, handleSaveCase } from '@/views/designTool/designTools';
import { useLoading } from '@/hooks/useLoading';

const userStore = useUserStore();
const { showLoading, hideLoading } = useLoading();
const dictStore = useDictStore();
const caseDataStore = useCaseDataStore();
let renderingQueue = Promise.resolve();

export function useDesignCase() {
  // 初始化响应式数据
  // const designData = ref(null);

  // 初始化保存方法
  const saveData = async (Engine, payload) => {
    const { data } = await Engine.exportPlan();
    console.log('导出数据', data);
    // 通过userStore内code判断是否保存过方案
    const isSave = 'code' in userStore.saveCaseData; // 存在code说明已保存
    // 这里可添加保存逻辑
    const params = {
      ...payload,
      toolData: JSON.stringify(data || {}),
      processingId: userStore.processingId || '',
    };
    if (isSave) {
      params.version = Number(userStore.version);
    }
    let res = await _API_saveIntelligentData(params);
    console.log('保存结果', res);
    if (res.code === '0') {
      message.success('保存成功!');
      // 保存成功后，将数据缓存到store
      userStore.setSaveCaseData(res?.data);
      userStore.setVersion(res?.data?.version);
      // 渲染图片
      const renderList = dictStore.systemList;
      const filteredList = renderList
        .filter((item) => caseDataStore.deviceSystemIds.includes(String(item.value)))
        .map((item) => ({
          systemId: item.value,
          systemName: item.label,
          type: '0',
        }));
      // 没有渲染图的系统 则不渲染
      if (filteredList.length === 0) return;
      showLoading({
        useCustomIcon: true,
        tip: `0/${filteredList.length}`,
      });
      try {
        await renderImgs(Engine, filteredList);
      } catch (error) {
        console.log('渲染图失败', error);
        hideLoading();
      }
    }
  };
  // 渲染图片列表
  const renderImgs = async (Engine, list) => {
    //  engine 为空 则不渲染
    if (!Engine) return;
    for (const [index, item] of list.entries()) {
      console.log('item', item);
      let img = await handleSaveCase(Engine, item);
      console.warn(`系统${item.systemName}的渲染图 ${index + 1}`, img);
      if (img) {
        storeRendering({
          systemId: item.systemId,
          img,
          systemName: item.systemName,
          type: item.type,
          index,
          length: list.length,
        });
      }
    }
  };

  // 渲染图上传
  const storeRendering = async ({ systemId, img, systemName, type, index, length }) => {
    renderingQueue = renderingQueue.then(async () => {
      try {
        const params = {
          code: userStore.saveCaseData.code,
          version: userStore.version,
          type: type, // 渲染图0 施工图1
        };
        const data = {
          systemId: systemId,
          systemName: systemName,
          url: img,
        };
        const res = await _API_saveIntelligentImage(data, params);
        if (res?.code === '0') {
          console.log('渲染图保存成功', res);
          if (index + 1 === length) {
            hideLoading();
          } else {
            showLoading({
              useCustomIcon: true,
              tip: `${index + 1}/${length}`,
            });
          }
          // type =1 施工图上传后需要储存在store
          if (type === '1') {
            await caseDataStore.setRenderingImg({ systemId, img, systemName, type });
          }
          await userStore.setVersion(res?.data?.version);
        } else {
          console.error('渲染图保存失败: ' + (res?.message || '未知错误'));
        }
      } catch (error) {
        console.error('storeRendering error:', error);
      }
    });
    await renderingQueue;
  };

  // 初始化撤销方法
  const undoData = (Engine) => {
    // 这里可添加撤销逻辑
    const res = Engine.undo();
    console.log('撤销', res);
  };

  // 初始化恢复方法
  const redoData = (Engine) => {
    // 这里可添加恢复逻辑
    const res = Engine.redo();
    console.log('已恢复操作', res);
  };

  // 初始化清空方法
  const clearData = (Engine) => {
    const res = Engine.clearAll();
    console.log('数据已清空', res);
    if (res.code == '0') {
      message.success('清空成功!');
    } else {
      message.warning('清空失败!');
    }
  };
  /**
   * 已绘制>1个封闭房间和在封闭房间内放置>1个商品 的系统列表
   * @param {Object} engineRef - 引擎实例的ref对象
   * @returns {Promise<Array>} 设备系统ID列表
   */
  const fetchDeviceSystemIds = async (engine) => {
    if (!engine) return [];
    try {
      const { data } = await engine.getDeviceSystemIds(false);
      // 将设备系统ID列表存到store中
      caseDataStore.setDeviceSystemIds(data);
      return data || [];
    } catch (error) {
      console.error('获取设备系统ID失败:', error);
      return [];
    }
  };

  /**
   * @description 获取方案状态，实际调用的是引擎的获取方案状态接口
   * @param {Object} engineRef - 引擎实例的ref对象
   * @returns {Promise<Object>} 方案状态数据
   */
  const getPlanStatusFromEngine = async (engine) => {
    if (!engine) return {};
    try {
      const { data } = await engine.getPlanStatus();
      console.log('获取方案状态', data);
      return data || {};
    } catch (error) {
      console.error('获取方案状态失败:', error);
      return {};
    }
  };

  /**
   * @description 生成提案的数据
   */
  async function createProposal(Engine) {
    return handleCreateProposalImg(Engine, {
      width: 800,
      height: 800,
      systemId: 'ALL',
      showAnnotations: true,
    });
  }

  return {
    saveData,
    undoData,
    redoData,
    clearData,
    fetchDeviceSystemIds,
    getPlanStatusFromEngine,
    renderImgs,
    createProposal,
  };
}
