import {
  Button,
  ConfigProvider,
  Form,
  Input,
  Row,
  Col,
  Tabs,
  Menu,
  Layout,
  Tooltip,
  Tree,
  Spin,
  Card,
  Badge,
  Divider,
  Dropdown,
  Select,
  Avatar,
  Tag,
  Table,
  Checkbox,
  Drawer,
  Modal,
  Alert,
  Descriptions,
  Empty,
  Popover,
  Popconfirm,
  PageHeader,
  Timeline,
  Pagination,
  Upload,
  Radio,
  List,
  Steps,
  Image,
  ImagePreviewGroup,
  DatePicker,
  RangePicker,
  Cascader,
  Result,
  Skeleton,
  InputNumber,
  Space,
  Switch,
  Progress,
  Collapse,
  CollapsePanel,
  Anchor,
} from 'ant-design-vue';

export default {
  install(app) {
    const useComponents = [
      Button,
      ConfigProvider,
      Form,
      Input,
      Row,
      Col,
      Tabs,
      Menu,
      Layout,
      Tooltip,
      Tree,
      Spin,
      Card,
      Badge,
      Divider,
      Dropdown,
      Select,
      Avatar,
      Tag,
      Table,
      Checkbox,
      Drawer,
      Modal,
      Alert,
      Descriptions,
      Empty,
      Popover,
      Popconfirm,
      PageHeader,
      Timeline,
      Pagination,
      Upload,
      Radio,
      List,
      Steps,
      Image,
      ImagePreviewGroup,
      DatePicker,
      RangePicker,
      Cascader,
      Result,
      Skeleton,
      InputNumber,
      Space,
      Switch,
      Progress,
      Collapse,
      CollapsePanel,
      Anchor,
    ];
    for (const component of useComponents) {
      app.use(component);
    }
  },
};
