.bgOpt {
  background-position: 0 0;
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

iconSet(a, b, c, d = 'png') {
  background-image: url('/public/images/' + c + '.' + d);
  background-size: a b;
  display: inline-flex;
  height: b;
  width: a;
  @extend .bgOpt;
}

// .i-phone-32 {
//   iconSet(32px, 32px, 'i-phone');
// }

// .i-add-20 {
//   iconSet(20px, 20px, 'i-add');
// }

// .i-sub-20 {
//   iconSet(20px, 20px, 'i-subtraction');
// }

.i-replace-16 {
  iconSet(16px, 16px, 'i-replace-goods');
}

