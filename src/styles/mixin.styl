

.sc-title {
  font-family: PingFangSC-Medium;
}

.flex-align-center {
  display: flex;
  align-items: center;
}

.flex-center {
  @extend .flex-align-center;
  justify-content: center;
}

.flex-spb {
  @extend .flex-align-center;
  justify-content: space-between;
}

.flex-st {
  @extend .flex-align-center;
  justify-content: flex-start;
}

.flex-end {
  @extend .flex-align-center;
  justify-content: flex-end;
}

.flex-around {
  @extend .flex-align-center;
  justify-content: space-around;
}

.ellipsis {
  width 100%;
  text-overflow ellipsis;
  white-space nowrap
  overflow hidden
}

.clearfix {
  &:after {
    content: '';
    display: table;
    clear: both;
  }
}

bRadius(n = 12px) {
  border-radius: n;
  overflow: hidden;
}

.box-shadow {
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.16);
}

.common-title-text {
  color: var(--opn-text-main);
  font-weight: 500;
  @extend .sc-title
}

.common-content-text {
  color: #333333;
  font-weight: 400;
}

.common-comment-text {
  color: #999999;
  font-weight: 400;
}

.title-large {
  font-size: 22px;
  @extend .common-title-text;
}

.title-medium {
  font-size: 20px;
  @extend .common-title-text;
}

.title-normal {
  font-size: 16px;
  @extend .common-title-text;
}

.title-small {
  font-size: 14px;
  @extend .common-title-text;
}

.title-comment-small
  @extend .title-small
  color rgba(0, 0, 0, 0.45)


.title-mini {
  font-size: 12px;
  @extend .common-title-text;
}

.content-normal {
  font-size: 14px;
  @extend .common-content-text;
}

.content-small {
  font-size: 12px;
  @extend .common-content-text;
}

.comment-small {
  font-size: 12px;
  @extend .common-comment-text;
}

.comment-normal {
  font-size: 14px;
  @extend .common-comment-text;
}

.comment-medium {
  font-size: 16px;
  @extend .common-comment-text;
}
