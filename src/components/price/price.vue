<template>
  <div class="price-wrap">
    <span class="price-small">￥</span>
    <span class="price">{{ priceInteger }}</span>
    <span class="price-small">.{{ priceDecimal }}</span>
  </div>
</template>

<script setup>
  import { computed } from 'vue';

  const props = defineProps({
    price: {
      type: [String, Number],
      default: '',
    },
    styleObj: {
      type: Object,
      default: () => ({
        price: '',
        color: '#FF2121',
        size: '12px',
        smallSize: '10px',
      }),
    },
  });

  // 处理价格的整数和小数部分
  const priceInteger = computed(() => {
    const price = parseFloat(props.price) || 0;
    return Math.floor(price);
  });

  const priceDecimal = computed(() => {
    const price = parseFloat(props.price) || 0;
    return (price % 1).toFixed(2).slice(2); // 取两位小数，去掉前面的0
  });
</script>

<style scoped lang="stylus">
  .price-wrap {
    display: flex;
    align-items: baseline;
    --price-color: v-bind('styleObj.color');
    --price-size: v-bind('styleObj.size');
    --price-small-size: v-bind('styleObj.smallSize');
  }

  .price {
    font-family: 'PingFang SC';
    font-weight: 500;
    font-size: var(--price-size);
    color: var(--price-color);
    line-height: 20px;
  }

  .price-small {
    font-weight: 500;
    font-size: var(--price-small-size);
    color: var(--price-color);
  }
</style>
