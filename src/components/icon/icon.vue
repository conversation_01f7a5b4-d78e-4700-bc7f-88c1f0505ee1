<script setup>
  import { computed, useAttrs } from 'vue';

  defineOptions({
    name: 'Icon',
  });
  const props = defineProps({
    icon: {
      type: String,
    },
    size: {
      type: Number,
      default: 16,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    pointer: {
      type: Boolean,
      default: false,
    },
    color: {
      type: String,
    },
    imgType: {
      type: String,
      default: 'png',
    },
  });

  // 使用 useAttrs 获取未被 props 定义的所有属性
  const attrs = useAttrs();

  const emit = defineEmits(['click']);

  /**
   * @description 是否是element-plus图标
   */
  const isAntdesignIcon = computed(() => {
    return /^[A-Z]/.test(props.icon);
  });

  /**
   * @description 是否是syn图标
   */
  const isSynIcon = computed(() => {
    const regex = /^syn[A-Z][a-zA-Z0-9]*$/;
    return regex.test(props.icon);
  });

  /**
   * @description 图片路径
   */
  const imgIcon = computed(() => {
    if (!isAntdesignIcon.value && !isSynIcon.value) {
      return require(`@/assets/icon/${props.icon}.${props.imgType}`);
    } else {
      return null;
    }
  });

  /**
   * @description 点击事件
   */
  function handleIconClick() {
    emit('click');
  }

  /**
   * @description 类名
   * @type {ComputedRef<string>}
   */
  const className = computed(() => {
    let baseName = 'zjsj-icon-wrap';
    if (props.pointer) {
      baseName += ' zjsj-icon-pointer';
    }
    if (props.loading) {
      baseName += ' zjsj-icon-loading';
    }
    return baseName;
  });

  /**
   * @description 样式对象
   */
  const styleObject = computed(() => {
    const style = {
      width: props.size + 'px',
      height: props.size + 'px',
    };
    if (props.color) {
      style.color = props.color;
    }
    return style;
  });

  /**
   * @description synIcon 样式对象
   */
  const synIconStyleObject = computed(() => {
    const style = {
      fontSize: props.size + 'px',
    };
    if (props.color) {
      style.color = props.color;
    }
    return style;
  });
</script>
<template>
  <div :class="className">
    <template v-if="isAntdesignIcon">
      <component :is="props.icon" @click.prevent="handleIconClick" v-bind="attrs"></component>
    </template>
    <template v-else-if="isSynIcon">
      <component
        :is="props.icon"
        :style="synIconStyleObject"
        @click.prevent="handleIconClick"
        v-bind="attrs"></component>
    </template>
    <template v-else>
      <slot name="default">
        <img
          :style="styleObject"
          :src="imgIcon"
          :width="props.size"
          :height="props.size"
          :alt="props.icon"
          draggable="false"
          @click="handleIconClick"
          v-bind="attrs" />
      </slot>
    </template>
  </div>
</template>

<style scoped lang="stylus">
  .zjsj-icon-wrap {
    width: auto;
    height: auto;
    overflow: hidden;
    display: inline-flex;
    vertical-align: sub;
  }

  .zjsj-icon-pointer {
    cursor: pointer;
  }

  .zjsj-icon-loading {
    animation: rotating 2s linear infinite;
  }

  @keyframes rotating {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>
