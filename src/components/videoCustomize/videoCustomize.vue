<template>
  <div class="videoCustomize" ref="videoContainer" @mouseover="data.showControl = true" @mouseout="data.showControl = false">
    <div class="top-section" v-if="showBg"></div>
    <div class="middle-section">
      <video
        ref="video"
        preload="auto"
        v-bind="$attrs"
        @click.stop="videoClick"
        @waiting="data.videoLoading = true"
        @playing="data.videoLoading = false"
        @canplay="handleCanPlay"
        @timeupdate="timeupdate"
        @progress="progress"
        muted
        @loadedmetadata="timeupdate"></video>
      <div
        v-show="data.videoLoading"
        class="loading-content"
        :element-loading-background="loadingBackground">
        <div></div>
      </div>
      <div v-show="showPlayControlBtn" class="circle" @click="clickPlay"></div>
    </div>
    <div class="video_controls" v-if="controls">
      <div
        :class="['bottom-section', showBg ? '' : 'hide-bg']"
        @mouseleave="progressMouseLeave()"
        @mouseup="progressMouseLeave()"
        @mousemove="progressMousemove">
        <div
          :class="[data.showControl === true ? 'show-control' : '']"
          class="progress_bar hide-control"
          ref="progressController"
          @click="progressClickHandler">
          <div class="cache" :style="{ width: data.cacheValue }"></div>
          <div class="done" :style="{ width: progressPercent }"></div>
          <i :style="{ left: progressPercent }" @mousedown="progressMousedown"></i>
        </div>
      </div>
      <div class="bottom-mask"></div>
      <img
        :class="[data.showControl === true ? 'show-control' : '']"
        :src="data.isPlay === true ? data.playImg : data.pauseImg"
        class="play hide-control"
        @click="data.isPlay === true ? pause() : play()" />
      <div :class="[data.showControl === true ? 'show-control' : '']" class="time hide-control">
        {{ data.currentTime }} / {{ data.durationTime }}
      </div>

      <div
        :class="[data.showControl === true ? 'show-control' : '']"
        class="sound hide-control"
        @mouseleave="data.soundTrigger = false"
        @mouseup="data.soundTrigger = false"
        @mousemove="soundMousemove">
        <div class="controller" ref="soundController" @click="soundClickHandler">
          <div :style="{ width: `${data.volume}px` }"></div>
          <i @mousedown="soundMousedown" :style="{ left: `${data.volume}px` }"></i>
        </div>
        <img @click="soundImgHandler" class="sound-img" :src="data.volume === 0 ? data.quitImg : data.soundImg" />
      </div>
      <img
        :class="[data.showControl === true ? 'show-control' : '']"
        class="full hide-control"
        :src="data.isFull === true ? data.shrinkImg : data.fullImg"
        @click="fullscreenToggle" />
    </div>
  </div>
</template>

<script setup>
  import { mediaLaunchFullscreen, mediaExitFullscreen } from '@/utils/tools';

  import { computed, nextTick, onMounted, reactive, ref } from 'vue'


  const props = defineProps({
    loadingBackground: {
      type: String,
      default: 'transparent'
    },
    controls: {
      type: Boolean,
      default: true
    },
    showBg: {
      type: Boolean,
      default: true
    },
    autoPlay: {
      type: Boolean,
      default: true
    },
    showPauseIcon: {
      type: Boolean,
      default: false
    }
  })

  const video = ref(null)
  const progressController = ref(null)
  const videoContainer = ref(null)
  const soundController = ref(null)

  const data = reactive({
    isCanPlay: false,
    isPlay: false, // 是否正在播放
    volume: 0, // 音量
    soundInfo: {}, // 音量区域的宽度、最左边和最右边
    soundTrigger: false, // 鼠标在音量键上按下
    isFull: false, // 是否全屏
    videoLoading: true, // 是否正在加载
    video: null,
    progressInfo: {}, // 进度条区域的宽度、最左边和最右边
    progressTrigger: false, // 鼠标在进度条上按下
    currentTime: '00:00', // 当前时间
    durationTime: '00:00', // 总共时间
    duration: 0, //总共时间的值
    progressValue: 0,
    cacheValue: '0%',
    showControl: false,
    // 引用的图标
    playImg: require('./play.png'),
    pauseImg: require('./pause.png'),
    soundImg: require('./sound.png'),
    quitImg: require('./quit.png'),
    fullImg: require('./full.png'),
    shrinkImg: require('./shrink.png'),
    statusChange: false,
    progressBeginX: 0,
    soundBeginX: 0,
  })

  const progressPercent = computed(() => {
    return `${((data.progressValue * 100) / data.progressInfo.width).toFixed(6)}%`;
  })

  const showPlayControlBtn = computed(() => {
    return props.showPauseIcon && data.isCanPlay && !data.isPlay;
  })

  onMounted(() => {
    // data.video = video.value;
    video.value.volume = 0;
    getProgressInfo();
    getSoundInfo();
  })

  function clickPlay() {
    play();
  }
  function videoClick(){
    data.isPlay === true ? pause() : play()
  }
  function handleCanPlay() {
    data.isCanPlay = true;
    if (props.autoPlay && data.isPlay===false) {
      nextTick().then(() => {
        play();
      });
    }
  }

  async function play() {
    try {
      data.isPlay = true;
      await video.value.play();
    } catch (err) {
      console.error('Error playing video:', err);
    }

  }

  async function pause() {
    try {
      data.isPlay = false;
      await video.value.pause();
    } catch (err) {
      console.error('Error playing video:', err);
    }

  }

  function getProgressInfo() {
    if (!props.controls) {
      return;
    }
    nextTick(() => {
      const { x, width } = progressController.value.getBoundingClientRect();
      data.progressInfo = { begin: x, width, end: x + width };
    });
  }

  function progressMousedown(e) {
    getProgressInfo();
    /* Warn: Unknown source: progressBeginX */
    data.progressBeginX = e.x;
    data.progressTrigger = true;
    if (data.isPlay === true) {
      // 如果正在播放，设置为暂停
      pause();
      data.statusChange = true;
    } else {

      data.statusChange = false;
    }
  }

  function progressMousemove({ x }) {
    const { begin, end, width } = data.progressInfo;
    if (data.progressTrigger === false || x < begin || x > end) {
      return false;
    }
    const xDiff = x -  data.progressBeginX;
    data.progressValue += xDiff;

    data.progressBeginX = x;
    processChange(width);
  }

  function processChange(width) {
    if (data.progressValue < 0) {
      data.progressValue = 0;
    }
    if (data.progressValue > width) {
      data.progressValue = width;
    }
    video.value.currentTime = (data.progressValue / width) * data.duration;
  }

  function progressClickHandler({ x }) {
    const { begin, width } = data.progressInfo;
    data.progressValue = x - begin;
    processChange(width);
  }

  function progressMouseLeave() {
    if (data.progressTrigger === true) {
      data.progressTrigger = false;
      if (data.statusChange === true) {
        // 如果鼠标按下时为播放，此时恢复为播放
        play();
      }
    }
  }

  function progress(e) {
    let { duration, buffered, currentTime } = e.target; // 视频总长度
    if (duration > 0) {
      for (var i = 0; i < buffered.length; i++) {
        // 寻找当前时间之后最近的点
        if (buffered.start(buffered.length - 1 - i) < currentTime) {
          data.cacheValue = (buffered.end(buffered.length - 1 - i) / duration) * 100 + '%';
          break;
        }
      }
    }
  }

  function fullscreenToggle() {
    if (data.isFull === false) {
      mediaLaunchFullscreen(videoContainer.value);
    } else {
      mediaExitFullscreen();
    }
    data.isFull = !data.isFull;
  }

  function timeupdate(e) {
    const { currentTime = 0, duration = 0 } = e.target;
    data.duration = duration;
    data.progressValue = Math.ceil((currentTime / duration) * data.progressInfo.width);
    data.currentTime = timeOperate(currentTime);
    data.durationTime = timeOperate(duration);
    if (currentTime === duration) {
      // 播放结束
      pause();
      video.value.currentTime = 0;
    }
    // data.videoLoading = false;
  }

  function timeOperate(time = 0) {
    if (Object.is(time, NaN)) {
      time = 0;
    }
    const second = parseInt(time);
    const minute = parseInt(time / 60);
    const hour = parseInt(minute / 60);
    return `${hour === 0 ? '' : `${hour.toString().padStart(2, '0')}:`}${(minute % 60)
      .toString()
      .padStart(2, '0')}:${(second % 60).toString().padStart(2, '0')}`;
  }

  function getSoundInfo() {
    if (!props.controls) {
      return;
    }
    const { x, width } = soundController.value.getBoundingClientRect();
    data.soundInfo = { begin: x, width, end: x + width };
  }

  function soundMousedown(e) {
    getSoundInfo();
    /* Warn: Unknown source: soundBeginX */
    data.soundBeginX = e.x;
    data.soundTrigger = true;
    setImgHandle();
  }

  function soundMousemove({ x }) {
    const { begin, end, width } = data.soundInfo;
    if (data.soundTrigger === false || x < begin || x > end) {
      return false;
    }
    const xDiff = x - /* Warn: Unknown source: soundBeginX */ data.soundBeginX;
    data.volume += xDiff;
    /* Warn: Unknown source: soundBeginX */
    data.soundBeginX = x;
    soundChange(width);
  }

  function soundClickHandler({ x }) {
    const { begin, width } = data.soundInfo;
    data.volume = x - begin;
    soundChange(width);
  }

  function soundChange(width) {
    if (data.volume < 0) {
      data.volume = 0;
    }
    if (data.volume > width) {
      data.volume = width;
    }
    video.value.volume = (data.volume / width).toFixed(1);
    setImgHandle();
  }

  function soundImgHandler() {
    data.volume = data.volume === 0 ? 20 : 0;
    video.value.volume = (data.volume / (data.soundInfo.width || 100)).toFixed(1);
    setImgHandle();
  }

  function setImgHandle() {
    let sound = data.volume;
    setMuted(sound === 0);
  }

  function setMuted(muted) {
    video.value.muted = muted;
  }
</script>

<style scoped lang="stylus">
  .videoCustomize {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    border-radius: 8px 0px 0px 8px;
    user-select: none;

    :deep(.el-loading-spinner) .path {
      stroke: white;
      stroke-width: 4;
    }

    .hide-control {
      opacity: 0;
      transition: opacity 0.3s;
      cursor: pointer;
    }

    .show-control {
      opacity: 1;
    }

    .middle-section {
      position: relative;
      flex: 1;

      video {
        position: absolute;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;

        &::-webkit-media-controls {
          display: none !important;
        }
      }

      .loading-content {
        position: absolute;
        left: 50%;
        top: 50%;
        width: 100px;
        height: 100px;
        transform: translate(-50%, -50%);
      }

      .circle {
        position: absolute;
        margin: 0;
        left: 50%;
        top: 50%;
        width: 90px;
        height: 90px;
        transform: translate(-50%, -50%);
        background: url('~@/assets/images/plan_video_play.png') no-repeat;
        background-size: 100%;
        z-index: 10;
        border: none;
        //border: solid 1px #333333;
        //border-radius: 50px;
        //height: 50px;
        //position: absolute;
        //width: 50px;
        //z-index: 10;
        //left: 44%;
        //top: 40%;
        //
        //.circle_inner_play {
        //  content: '';
        //  display: block;
        //  width: 0;
        //  height: 0;
        //  border-style: solid;
        //  border-width: 10px 0 10px 20px;
        //  border-color: transparent transparent transparent #333333;
        //  position: absolute;
        //  top: 50%;
        //  left: 50%;
        //  margin: -10px 0 0 -7px;
        //}
      }
    }

    .top-section,
    .bottom-section {
      position: relative;
      height: 49px;
      background-color: black;
      z-index: 1;
      /* 进度条 */

      .progress_bar {
        position: absolute;
        bottom: 20px;
        left: 20px;
        width: calc(100% - 40px);
        height: 4px;
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 2px;

        > div {
          position: absolute;
          width: 0;
          height: 100%;
          border-radius: 2px;

          &.done {
            background-color: #be965a;
          }

          &.cache {
            background-color: #999;
          }
        }

        > i {
          position: absolute;
          top: 50%;
          left: 0;
          transform: translate(-50%, -50%);
          width: 12px;
          height: 12px;
          background-color: white;
          border-radius: 50%;
        }
      }
    }

    .hide-bg {
      height: 0;
    }

    .bottom-mask {
      position: absolute;
      width: 100%;
      height: 124px;
      bottom: 0;
      left: 0;
      pointer-events: none;
      background-image: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #1e1e1e 100%);
      border-radius: 0px 0px 0px 8px;
    }

    img {
      width: 24px;
      height: 24px;
    }

    .play {
      position: absolute;
      left: 20px;
      bottom: 44px;
      z-index: 1;
    }

    .time {
      position: absolute;
      left: 64px;
      bottom: 46px;
      color: white;
      white-space: nowrap;
      font-size: 14px;
      z-index: 1;
    }

    /* 音量 */

    .sound {
      position: absolute;
      width: 138px;
      height: 36px;
      right: 62px;
      bottom: 38px;
      background-color: rgba(51, 51, 51, 0.5);
      border-radius: 18px;
      z-index: 1;

      .controller {
        position: absolute;
        left: 22px;
        top: 50%;
        width: 64px;
        height: 4px;
        transform: translateY(-50%);
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 2px;

        > div {
          position: absolute;
          height: 100%;
          background-color: #fff;
          border-radius: 2px;
        }

        > i {
          position: absolute;
          left: 0;
          top: 50%;
          width: 12px;
          height: 12px;
          transform: translate(-50%, -50%);
          border-radius: 50%;
          background-color: white;
        }
      }

      img {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    /* 全屏 */

    .full {
      position: absolute;
      bottom: 44px;
      right: 20px;
      cursor: pointer;
      z-index: 1;
    }
  }
</style>
