/**
 * CustomTabs 组件的 JSDoc 类型定义
 */

/**
 * @typedef {Object} TabItem
 * @property {string} key - 标签页的唯一标识符
 * @property {string} title - 标签页显示的标题
 * @property {boolean} [showDot] - 是否显示红色圆点指示器（仅在激活状态时可见）
 * @property {boolean} [disabled] - 标签页是否禁用
 */

/**
 * @typedef {Object} CustomTabsProps
 * @property {TabItem[]} tabs - 标签页项目数组
 * @property {string} [activeKey] - 当前激活的标签页键值（受控模式）
 * @property {string} [defaultActiveKey] - 默认激活的标签页键值（非受控模式）
 * @property {string} [position] - 标签页导航栏位置：'left'(左对齐) | 'center'(居中) | 'right'(右对齐)，默认为 'left'
 * @property {boolean} [keepAlive] - 是否保持标签页内容状态，避免切换时重新渲染，默认为 true
 */

/**
 * @typedef {Object} CustomTabsEmits
 * @property {function(string): void} change - 标签页切换时触发
 * @property {function(string): void} update:activeKey - 支持 v-model:activeKey 双向绑定
 */

/**
 * 使用示例:
 *
 * <CustomTabs
 *   :tabs="tabItems"
 *   v-model:activeKey="currentTab"
 *   @change="handleTabChange"
 * >
 *   <template #tab1="{ activeKey, activeTab }">
 *     <div>标签页1的内容</div>
 *   </template>
 *   <template #tab2="{ activeKey, activeTab }">
 *     <div>标签页2的内容</div>
 *   </template>
 * </CustomTabs>
 *
 * @example
 * const tabItems = [
 *   {
 *     key: 'tab1',
 *     title: '从户型库选择',
 *     showDot: true
 *   },
 *   {
 *     key: 'tab2',
 *     title: '手动选择'
 *   }
 * ]
 */

export {};
