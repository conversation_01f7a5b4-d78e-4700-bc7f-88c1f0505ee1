<template>
  <div class="custom-tabs-container">
    <div class="custom-tabs-nav" :class="`custom-tabs-nav--${position}`">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        :class="[
          'custom-tab-item',
          {
            'custom-tab-active': activeKey === tab.key,
            'custom-tab-inactive': activeKey !== tab.key,
          },
        ]"
        @click="handleTabClick(tab.key)">
        <span class="custom-tab-title">{{ tab.title }}</span>
        <div v-if="tab.showDot && activeKey === tab.key" class="custom-tab-dot"></div>
      </div>
    </div>
    <div class="custom-tabs-content">
      <KeepAlive v-if="keepAlive">
        <div v-for="tab in tabs" :key="tab.key" v-show="tab.key === activeKey" class="custom-tab-pane">
          <slot :name="tab.key" :activeKey="activeKey" :activeTab="activeTab" />
        </div>
      </KeepAlive>
      <div v-else>
        <slot :name="activeKey" :activeKey="activeKey" :activeTab="activeTab" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, watch } from 'vue';

  // 组件定义
  defineOptions({
    name: 'CustomTabs',
  });

  const props = defineProps({
    tabs: {
      type: Array,
      default: () => [],
      required: true,
    },
    activeKey: {
      type: String,
      default: '',
    },
    defaultActiveKey: {
      type: String,
      default: '',
    },
    position: {
      type: String,
      default: 'left',
      validator: (value) => ['left', 'center', 'right'].includes(value),
    },
    keepAlive: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['change', 'update:activeKey']);

  // 响应式状态
  const internalActiveKey = ref(props.activeKey || props.defaultActiveKey || (props.tabs[0]?.key ?? ''));

  // 计算属性
  const activeKey = computed({
    get: () => props.activeKey || internalActiveKey.value,
    set: (value) => {
      internalActiveKey.value = value;
      emit('update:activeKey', value);
    },
  });

  const activeTab = computed(() => props.tabs.find((tab) => tab.key === activeKey.value));

  // 方法
  const handleTabClick = (key) => {
    const tab = props.tabs.find((t) => t.key === key);
    if (tab?.disabled) return;

    activeKey.value = key;
    emit('change', key);
  };

  // 监听外部 activeKey 变化
  watch(
    () => props.activeKey,
    (newKey) => {
      if (newKey && newKey !== internalActiveKey.value) {
        internalActiveKey.value = newKey;
      }
    },
    { immediate: true }
  );
</script>

<style scoped lang="stylus">
  // 自定义标签页容器
  .custom-tabs-container
    width 100%
  r
    // 标签页导航栏
  .custom-tabs-nav
    display flex
    align-items center
    justify-content center
    padding 0 6px
    height 44px
    background-color #F3F3F3
    border-radius 10px
    width fit-content

    // 位置控制
    &--left
      margin-left 0
      margin-right auto

    &--center
      margin-left auto
      margin-right auto

    &--right
      margin-left auto
      margin-right 0

  // 标签页项目
  .custom-tab-item
    position relative
    display flex
    align-items center
    cursor pointer
    transition all 0.2s ease

    // 激活状态的标签页
    &.custom-tab-active
      height 32px
      padding 0 16px
      background-color #FFFFFF
      border-radius 8px
      box-shadow 0px 1px 3px 0px rgba(0, 0, 0, 0.05)
      gap 12px

      .custom-tab-title
        font-family 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif
        font-weight 500
        font-size 16px
        line-height 1.5em
        color rgba(0, 0, 0, 0.85)

    // 非激活状态的标签页
    &.custom-tab-inactive
      height 36px
      padding 0 16px
      gap 16px

      .custom-tab-title
        font-family 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif
        font-weight 400
        font-size 16px
        line-height 1.5em
        color rgba(0, 0, 0, 0.6)

      // 悬停状态
      &:hover
        .custom-tab-title
          color rgba(0, 0, 0, 0.75)

  // 标签页标题
  .custom-tab-title
    white-space nowrap
    text-align center
    user-select none

  // 红色圆点指示器
  .custom-tab-dot
    width 8px
    height 8px
    background-color #ff4d4f
    border-radius 50%
    flex-shrink 0

  // 标签页内容区域
  .custom-tabs-content
    margin-top 16px

  // 标签页面板
  .custom-tab-pane
    width 100%
</style>
