# Popover 组件

一个支持函数式调用的 Popover 组件，可以在屏幕任意位置显示，内部使用 ant-design-vue 的 tooltip 组件。

## 基本用法

```javascript
import { usePopover } from '@/components/popover'

// 基本使用
usePopover({
  placement: 'top',
  content: '这是一个提示内容',
  x: 100,
  y: 100
})
```

## API

### usePopover(options)

#### 参数

| 参数 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| placement | string | 'top' | 弹出位置，可选值：'top', 'left', 'right', 'bottom', 'topLeft', 'topRight', 'bottomLeft', 'bottomRight', 'leftTop', 'leftBottom', 'rightTop', 'rightBottom' |
| content | string\|object\|function | '' | 弹出内容，可以是HTML字符串、Vue组件或渲染函数 |
| contentProps | object | {} | 传递给内容组件的props |
| x | number | 0 | X坐标位置 |
| y | number | 0 | Y坐标位置 |
| onClose | function | null | 关闭回调函数 |

#### 返回值

| 属性 | 类型 | 说明 |
| --- | --- | --- |
| close | function | 手动关闭popover的方法 |

## 使用示例

### 1. 显示文本内容

```javascript
usePopover({
  placement: 'top',
  content: '这是一个简单的文本提示',
  x: 200,
  y: 100
})
```

### 2. 显示HTML内容

```javascript
usePopover({
  placement: 'bottom',
  content: '<div style="color: red;">这是<strong>HTML</strong>内容</div>',
  x: 300,
  y: 200
})
```

### 3. 显示Vue组件

```javascript
import MyComponent from './MyComponent.vue'

usePopover({
  placement: 'right',
  content: MyComponent,
  contentProps: {
    title: '标题',
    data: { id: 1, name: '测试' }
  },
  x: 400,
  y: 300
})
```

### 4. 在鼠标位置显示

```javascript
import { showPopoverAtEvent } from '@/components/popover'

// 在点击事件中使用
function handleClick(event) {
  showPopoverAtEvent(event, {
    placement: 'top',
    content: '在鼠标位置显示的提示'
  })
}
```

### 5. 在指定元素附近显示

```javascript
import { showPopoverAtElement } from '@/components/popover'

// 在指定元素附近显示
showPopoverAtElement('#my-button', {
  placement: 'top',
  content: '在按钮上方显示的提示'
})
```

### 6. 带关闭回调

```javascript
const popover = usePopover({
  placement: 'top',
  content: '这是一个提示',
  x: 100,
  y: 100,
  onClose: () => {
    console.log('Popover 已关闭')
  }
})

// 也可以手动关闭
// popover.close()
```

## 注意事项

1. 组件使用 `teleport` 将内容渲染到 `body` 元素中
2. 支持点击外部区域自动关闭
3. 坐标系统基于浏览器窗口的绝对定位
4. 内容支持字符串、HTML、Vue组件等多种形式
5. 组件会自动处理清理工作，无需手动管理内存