<template>
  <teleport to="body">
    <div
      class="popover-container"
      :style="{
        position: 'fixed',
        left: x + 'px',
        top: y + 'px',
        zIndex: 9999,
      }"
      ref="popoverRef">
      <a-popover :placement="placement" overlayClassName="device-set-popover" :open="tooltipVisible" trigger="none">
        <template #content>
          <div v-if="typeof content === 'string'" v-html="content"></div>
          <component v-else-if="content" :is="content" v-bind="contentProps"></component>
        </template>
        <div class="popover-trigger"></div>
      </a-popover>
    </div>
  </teleport>
</template>

<script setup>
  import { ref, watch, onMounted, onUnmounted } from 'vue';
  defineOptions({
    name: 'PopoverComponent',
  });

  const props = defineProps({
    visible: {
      type: Boolean,
      default: true,
    },
    placement: {
      type: String,
      default: 'top',
    },
    content: {
      type: [String, Object, Function],
      default: '',
    },
    contentProps: {
      type: Object,
      default: () => ({}),
    },
    x: {
      type: Number,
      default: 0,
    },
    y: {
      type: Number,
      default: 0,
    },
    onClose: {
      type: Function,
      default: null,
    },
  });

  const tooltipVisible = ref(true);
  const popoverRef = ref(null);

  // 监听visible变化
  watch(
    () => props.visible,
    (newVal) => {
      if (newVal) {
        // 立即显示popover
        tooltipVisible.value = true;
      } else {
        tooltipVisible.value = false;
      }
    }
  );

  // 监听tooltip显示状态变化
  watch(tooltipVisible, (newVal) => {
    if (!newVal && props.onClose) {
      props.onClose();
    }
  });

  // 点击外部关闭
  const handleClickOutside = (event) => {
    if (popoverRef.value && !popoverRef.value.contains(event.target)) {
      tooltipVisible.value = false;
    }
  };

  // 组件挂载时立即显示popover
  onMounted(() => {
    if (props.visible) {
      // 立即显示，不需要等待nextTick
      tooltipVisible.value = true;
    }
    // 延迟添加点击外部关闭的事件监听，避免立即触发
    setTimeout(() => {
      document.addEventListener('click', handleClickOutside);
    }, 100);
  });

  // 组件卸载时清理事件监听
  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside);
  });
</script>

<style scoped>
  .popover-container {
    pointer-events: auto;
  }

  .popover-trigger {
    pointer-events: auto;
    width: 1px;
    height: 1px;
    background: transparent;
  }
</style>
<style lang="stylus">
  .device-set-popover{
    padding 4px !important
    .ant-popover-inner {
      padding 0 !important
    }
    .ant-popover-arrow {
      display none !important
    }
  }
</style>
