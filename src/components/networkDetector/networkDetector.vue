<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Modal, message } from 'ant-design-vue'

defineOptions({
  name: 'NetworkDetector'
})

const props = defineProps({
  // 是否启用网络检测
  enabled: {
    type: Boolean,
    default: true
  },
  // 断网提示标题
  offlineTitle: {
    type: String,
    default: '网络连接已断开'
  },
  // 断网提示内容
  offlineContent: {
    type: String,
    default: '请检查您的网络连接，确保网络正常后重试。'
  },
  // 网络恢复提示内容
  onlineMessage: {
    type: String,
    default: '网络连接已恢复'
  },
  // 检测间隔（毫秒）
  checkInterval: {
    type: Number,
    default: 3000
  }
})

const emit = defineEmits(['network-change', 'offline', 'online'])

// 网络状态
const isOnline = ref(navigator.onLine)
const wasOffline = ref(false)
let checkTimer = null
let offlineModal = null

// 网络状态变化处理
const handleNetworkChange = (online) => {
  const previousState = isOnline.value
  isOnline.value = online
  
  emit('network-change', { online, previousState })
  
  if (online) {
    handleOnline()
  } else {
    handleOffline()
  }
}

// 处理断网
const handleOffline = () => {
  if (!props.enabled) return
  
  wasOffline.value = true
  emit('offline')
  
  // 显示断网弹窗
  if (!offlineModal) {
    offlineModal = Modal.error({
      title: props.offlineTitle,
      content: props.offlineContent,
      okText: '我知道了',
      centered: true,
      maskClosable: false,
      closable: false,
      onOk: () => {
        offlineModal = null
      }
    })
  }
}

// 处理网络恢复
const handleOnline = () => {
  if (!props.enabled) return
  
  emit('online')
  
  // 关闭断网弹窗
  if (offlineModal) {
    offlineModal.destroy()
    offlineModal = null
  }
  
  // 如果之前是断网状态，显示恢复提示
  if (wasOffline.value) {
    message.success(props.onlineMessage)
    wasOffline.value = false
  }
}

// 在线状态监听器
const onlineHandler = () => handleNetworkChange(true)
const offlineHandler = () => handleNetworkChange(false)

// 定期检测网络状态（作为备用检测机制）
const startPeriodicCheck = () => {
  if (!props.enabled || props.checkInterval <= 0) return
  
  checkTimer = setInterval(() => {
    const currentOnlineState = navigator.onLine
    if (currentOnlineState !== isOnline.value) {
      handleNetworkChange(currentOnlineState)
    }
  }, props.checkInterval)
}

// 停止定期检测
const stopPeriodicCheck = () => {
  if (checkTimer) {
    clearInterval(checkTimer)
    checkTimer = null
  }
}

// 组件挂载
onMounted(() => {
  if (!props.enabled) return
  
  // 添加网络状态监听器
  window.addEventListener('online', onlineHandler)
  window.addEventListener('offline', offlineHandler)
  
  // 启动定期检测
  startPeriodicCheck()
  
  // 初始状态检测
  handleNetworkChange(navigator.onLine)
})

// 组件卸载
onUnmounted(() => {
  // 移除事件监听器
  window.removeEventListener('online', onlineHandler)
  window.removeEventListener('offline', offlineHandler)
  
  // 停止定期检测
  stopPeriodicCheck()
  
  // 清理弹窗
  if (offlineModal) {
    offlineModal.destroy()
    offlineModal = null
  }
})

// 暴露方法给父组件
defineExpose({
  isOnline: () => isOnline.value,
  checkNetwork: () => handleNetworkChange(navigator.onLine),
  forceOffline: () => handleOffline(),
  forceOnline: () => handleOnline()
})
</script>

<template>
  <div style="display: none;"><!-- 网络检测组件，无需渲染内容 --></div>
</template>

<style scoped>
/* 网络检测组件无需样式 */
</style>