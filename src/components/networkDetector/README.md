# NetworkDetector 网络检测组件

一个用于检测网络连接状态的Vue 3组件，支持断网弹窗提示和网络恢复消息提示。

## 功能特性

- 🌐 实时监测网络连接状态
- 🚨 断网时自动弹窗提示
- ✅ 网络恢复时显示成功消息
- 🔄 支持定期检测作为备用机制
- 🎛️ 可配置的提示内容和检测间隔
- 📱 支持组件和Hook两种使用方式
- 🎯 提供丰富的事件回调

## 使用方式

### 方式一：组件使用

```vue
<template>
  <div>
    <!-- 其他内容 -->
    
    <!-- 网络检测组件 -->
    <NetworkDetector 
      :enabled="true"
      offline-title="网络已断开"
      offline-content="请检查网络连接"
      online-message="网络已恢复"
      :check-interval="3000"
      @network-change="handleNetworkChange"
      @offline="handleOffline"
      @online="handleOnline"
    />
  </div>
</template>

<script setup>
import NetworkDetector from '@/components/networkDetector'

const handleNetworkChange = ({ online, previousState }) => {
  console.log('网络状态变化:', online ? '在线' : '离线')
}

const handleOffline = () => {
  console.log('网络已断开')
  // 可以在这里添加断网时的业务逻辑
}

const handleOnline = () => {
  console.log('网络已恢复')
  // 可以在这里添加网络恢复时的业务逻辑
}
</script>
```

### 方式二：Hook使用

```vue
<template>
  <div>
    <p>网络状态: {{ isOnline ? '在线' : '离线' }}</p>
    <button @click="checkNetwork">手动检测</button>
    <button @click="forceOffline">模拟断网</button>
    <button @click="forceOnline">模拟恢复</button>
  </div>
</template>

<script setup>
import { useNetworkDetector } from '@/components/networkDetector'

const { 
  isOnline, 
  checkNetwork, 
  forceOffline, 
  forceOnline 
} = useNetworkDetector({
  enabled: true,
  offlineTitle: '自定义断网标题',
  offlineContent: '自定义断网内容',
  onlineMessage: '自定义恢复消息',
  checkInterval: 5000,
  onOffline: () => {
    console.log('Hook: 网络断开')
  },
  onOnline: () => {
    console.log('Hook: 网络恢复')
  },
  onNetworkChange: ({ online }) => {
    console.log('Hook: 网络状态变化', online)
  }
})
</script>
```

## 组件Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| enabled | Boolean | true | 是否启用网络检测 |
| offlineTitle | String | '网络连接已断开' | 断网提示标题 |
| offlineContent | String | '请检查您的网络连接...' | 断网提示内容 |
| onlineMessage | String | '网络连接已恢复' | 网络恢复提示内容 |
| checkInterval | Number | 3000 | 检测间隔（毫秒），设为0禁用定期检测 |

## 组件Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| network-change | { online, previousState } | 网络状态变化时触发 |
| offline | - | 网络断开时触发 |
| online | - | 网络恢复时触发 |

## Hook选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| enabled | Boolean | true | 是否启用网络检测 |
| offlineTitle | String | '网络连接已断开' | 断网提示标题 |
| offlineContent | String | '请检查您的网络连接...' | 断网提示内容 |
| onlineMessage | String | '网络连接已恢复' | 网络恢复提示内容 |
| checkInterval | Number | 3000 | 检测间隔（毫秒） |
| onOffline | Function | - | 断网回调函数 |
| onOnline | Function | - | 网络恢复回调函数 |
| onNetworkChange | Function | - | 网络状态变化回调函数 |

## Hook返回值

| 属性/方法 | 类型 | 说明 |
|-----------|------|------|
| isOnline | Ref\<Boolean> | 当前网络状态 |
| wasOffline | Ref\<Boolean> | 是否曾经断网 |
| startDetection | Function | 启动网络检测 |
| stopDetection | Function | 停止网络检测 |
| checkNetwork | Function | 手动检测网络状态 |
| forceOffline | Function | 强制触发断网状态（测试用） |
| forceOnline | Function | 强制触发在线状态（测试用） |

## 组件暴露的方法

通过ref可以访问组件的以下方法：

```vue
<template>
  <NetworkDetector ref="networkDetectorRef" />
</template>

<script setup>
import { ref } from 'vue'

const networkDetectorRef = ref()

// 获取当前网络状态
const isOnline = networkDetectorRef.value?.isOnline()

// 手动检测网络
networkDetectorRef.value?.checkNetwork()

// 强制触发断网（测试用）
networkDetectorRef.value?.forceOffline()

// 强制触发在线（测试用）
networkDetectorRef.value?.forceOnline()
</script>
```

## 注意事项

1. **浏览器兼容性**: 依赖于 `navigator.onLine` API，在某些环境下可能不够准确
2. **定期检测**: 作为备用机制，建议设置合理的检测间隔，避免过于频繁
3. **弹窗管理**: 断网弹窗会自动管理，避免重复弹出
4. **内存清理**: 组件卸载时会自动清理所有事件监听器和定时器
5. **测试方法**: 可以通过浏览器开发者工具的Network面板模拟网络状态

## 最佳实践

1. **全局使用**: 建议在应用的根组件中使用，确保全局网络状态监控
2. **业务集成**: 结合具体业务需求，在网络状态变化时执行相应的业务逻辑
3. **用户体验**: 可以根据网络状态调整应用的功能和界面显示
4. **错误处理**: 在网络请求中结合网络状态进行更好的错误处理

```vue
<!-- 推荐的全局使用方式 -->
<template>
  <div id="app">
    <router-view />
    <NetworkDetector @offline="handleGlobalOffline" @online="handleGlobalOnline" />
  </div>
</template>

<script setup>
import NetworkDetector from '@/components/networkDetector'
import { useStore } from 'vuex'

const store = useStore()

const handleGlobalOffline = () => {
  // 设置全局离线状态
  store.commit('setNetworkStatus', false)
  // 暂停所有网络请求
  // 显示离线模式界面
}

const handleGlobalOnline = () => {
  // 设置全局在线状态
  store.commit('setNetworkStatus', true)
  // 重新发送失败的请求
  // 恢复正常界面
}
</script>
```