import { ref, onMounted, onUnmounted } from 'vue'
import { Modal, message } from 'ant-design-vue'

/**
 * 网络检测Hook
 * @param {Object} options 配置选项
 * @param {Boolean} options.enabled 是否启用网络检测，默认true
 * @param {String} options.offlineTitle 断网提示标题
 * @param {String} options.offlineContent 断网提示内容
 * @param {String} options.onlineMessage 网络恢复提示内容
 * @param {Number} options.checkInterval 检测间隔（毫秒），默认3000
 * @param {Function} options.onOffline 断网回调
 * @param {Function} options.onOnline 网络恢复回调
 * @param {Function} options.onNetworkChange 网络状态变化回调
 * @returns {Object} 返回网络状态和控制方法
 */
export default function useNetworkDetector(options = {}) {
  const {
    enabled = true,
    offlineTitle = '网络连接已断开',
    offlineContent = '请检查您的网络连接，确保网络正常后重试。',
    onlineMessage = '网络连接已恢复',
    checkInterval = 3000,
    onOffline,
    onOnline,
    onNetworkChange
  } = options

  // 网络状态
  const isOnline = ref(navigator.onLine)
  const wasOffline = ref(false)
  let checkTimer = null
  let offlineModal = null

  // 网络状态变化处理
  const handleNetworkChange = (online) => {
    const previousState = isOnline.value
    isOnline.value = online
    
    // 触发回调
    onNetworkChange && onNetworkChange({ online, previousState })
    
    if (online) {
      handleOnline()
    } else {
      handleOffline()
    }
  }

  // 处理断网
  const handleOffline = () => {
    if (!enabled) return
    
    wasOffline.value = true
    
    // 触发回调
    onOffline && onOffline()
    
    // 显示断网弹窗
    if (!offlineModal) {
      offlineModal = Modal.error({
        title: offlineTitle,
        content: offlineContent,
        okText: '我知道了',
        centered: true,
        maskClosable: false,
        closable: false,
        onOk: () => {
          offlineModal = null
        }
      })
    }
  }

  // 处理网络恢复
  const handleOnline = () => {
    if (!enabled) return
    
    // 触发回调
    onOnline && onOnline()
    
    // 关闭断网弹窗
    if (offlineModal) {
      offlineModal.destroy()
      offlineModal = null
    }
    
    // 如果之前是断网状态，显示恢复提示
    if (wasOffline.value) {
      message.success(onlineMessage)
      wasOffline.value = false
    }
  }

  // 在线状态监听器
  const onlineHandler = () => handleNetworkChange(true)
  const offlineHandler = () => handleNetworkChange(false)

  // 定期检测网络状态（作为备用检测机制）
  const startPeriodicCheck = () => {
    if (!enabled || checkInterval <= 0) return
    
    checkTimer = setInterval(() => {
      const currentOnlineState = navigator.onLine
      if (currentOnlineState !== isOnline.value) {
        handleNetworkChange(currentOnlineState)
      }
    }, checkInterval)
  }

  // 停止定期检测
  const stopPeriodicCheck = () => {
    if (checkTimer) {
      clearInterval(checkTimer)
      checkTimer = null
    }
  }

  // 启动网络检测
  const startDetection = () => {
    if (!enabled) return
    
    // 添加网络状态监听器
    window.addEventListener('online', onlineHandler)
    window.addEventListener('offline', offlineHandler)
    
    // 启动定期检测
    startPeriodicCheck()
    
    // 初始状态检测
    handleNetworkChange(navigator.onLine)
  }

  // 停止网络检测
  const stopDetection = () => {
    // 移除事件监听器
    window.removeEventListener('online', onlineHandler)
    window.removeEventListener('offline', offlineHandler)
    
    // 停止定期检测
    stopPeriodicCheck()
    
    // 清理弹窗
    if (offlineModal) {
      offlineModal.destroy()
      offlineModal = null
    }
  }

  // 手动检测网络状态
  const checkNetwork = () => {
    handleNetworkChange(navigator.onLine)
  }

  // 强制触发断网状态（用于测试）
  const forceOffline = () => {
    handleOffline()
  }

  // 强制触发在线状态（用于测试）
  const forceOnline = () => {
    handleOnline()
  }

  // 自动启动检测（在组件挂载时）
  onMounted(() => {
    startDetection()
  })

  // 自动停止检测（在组件卸载时）
  onUnmounted(() => {
    stopDetection()
  })

  return {
    // 状态
    isOnline,
    wasOffline,
    
    // 方法
    startDetection,
    stopDetection,
    checkNetwork,
    forceOffline,
    forceOnline
  }
}