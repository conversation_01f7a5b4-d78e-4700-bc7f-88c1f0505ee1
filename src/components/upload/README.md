# 上传组件改造说明

## 改造内容

### 1. 新增 Props 属性

为上传组件新增了两个 Props 属性来控制组件尺寸：

```javascript
width: {
  type: [Number, String],
  default: 36,
},
height: {
  type: [Number, String],
  default: 36,
},
```

- `width`: 组件宽度，支持数字（px）或字符串（如 "5rem", "100%"）
- `height`: 组件高度，支持数字（px）或字符串（如 "5rem", "100%"）
- 默认值保持原来的 36px

### 2. 上传进度和状态显示

新增了完整的上传状态管理和视觉反馈：

#### 上传状态
- **上传中 (uploading)**: 显示圆形进度条和百分比
- **上传成功 (done)**: 显示图片预览和删除按钮
- **上传失败 (error)**: 显示错误提示和重试按钮

#### 视觉效果
- 上传中：蓝色边框 + 半透明遮罩 + 圆形进度条
- 上传失败：红色边框 + 错误图标 + 重试按钮
- 交互按钮：悬停效果 + 缩放动画

### 3. 动态样式计算

添加了计算属性 `uploadSizeStyle` 来处理尺寸样式：

```javascript
const uploadSizeStyle = computed(() => {
  const width = typeof props.width === 'number' ? `${props.width}px` : props.width;
  const height = typeof props.height === 'number' ? `${props.height}px` : props.height;
  return {
    width,
    height,
  };
});
```

### 4. CSS 变量实现

使用 CSS 变量来实现动态尺寸控制：

```stylus
.uploader-container {
  --upload-width: 36px;
  --upload-height: 36px;
}

.select-card{
  width: var(--upload-width) !important;
  height: var(--upload-height) !important;
}
```

### 5. 模板结构调整

在模板外层添加了容器 div，用于传递 CSS 变量：

```vue
<div class="uploader-container" :style="{ '--upload-width': uploadSizeStyle.width, '--upload-height': uploadSizeStyle.height }">
  <a-upload>
    <!-- 原有内容 -->
  </a-upload>
</div>
```

### 6. 重试上传功能

新增了 `retryUpload` 方法，支持上传失败后重新尝试：

```javascript
function retryUpload(file) {
  // 重置文件状态
  file.status = 'uploading';
  file.percent = 0;

  // 重新执行上传逻辑
  // ...
}
```

### 7. 智能添加按钮控制与布局稳定

通过固定容器尺寸解决布局跳动问题：

```javascript
const showAdd = computed(() => {
  // 检查是否有文件正在上传
  const hasUploading = uploadList.value.some(file => file.status === 'uploading');

  // 如果有文件正在上传，隐藏添加按钮
  if (hasUploading) {
    return false;
  }

  // 原有逻辑：文件数量未达到最大值或强制显示添加按钮
  return uploadList.value.length < props.max || props.showAddBtn;
});
```

```stylus
.uploader-container {
  --upload-width: 36px;
  --upload-height: 36px;

  // 为整个容器设置固定尺寸，避免布局跳动
  :deep(.ant-upload-wrapper) {
    .ant-upload-list {
      .ant-upload-select {
        width: var(--upload-width) !important;
        height: var(--upload-height) !important;
        min-width: var(--upload-width) !important;
        min-height: var(--upload-height) !important;
      }
    }
  }
}
```

**功能说明：**
- 上传过程中隐藏添加按钮，避免与上传进度效果冲突
- 通过固定容器的 `width`、`height`、`min-width`、`min-height` 确保布局稳定
- 使用 CSS 变量动态控制尺寸，保持响应式特性
- 无论添加按钮显示或隐藏，容器尺寸始终保持一致

## 使用方法

### 基础用法（默认尺寸 36x36）

```vue
<uploader 
  :fileList="fileList"
  @onChange="handleChange"
/>
```

### 自定义数字尺寸

```vue
<uploader 
  :fileList="fileList"
  :width="60"
  :height="60"
  @onChange="handleChange"
/>
```

### 自定义字符串尺寸

```vue
<uploader 
  :fileList="fileList"
  width="5rem"
  height="4rem"
  @onChange="handleChange"
/>
```

### 不同比例尺寸

```vue
<uploader 
  :fileList="fileList"
  :width="100"
  :height="80"
  @onChange="handleChange"
/>
```

## 兼容性说明

- 保持了所有原有的 Props 和功能
- 默认尺寸仍为 36x36，确保向后兼容
- 所有原有的样式类名和结构保持不变
- 只是将固定的尺寸改为可配置的动态尺寸

## 功能特性

### 上传状态管理
- ✅ 实时进度显示（圆形进度条 + 百分比）
- ✅ 上传失败重试功能
- ✅ 取消上传功能
- ✅ 删除文件功能
- ✅ 状态视觉反馈（边框颜色、背景色）
- ✅ **智能添加按钮**：上传过程中自动隐藏添加按钮，通过固定容器尺寸避免布局跳动

### 交互体验
- ✅ 按钮悬停效果
- ✅ 缩放动画
- ✅ 状态图标显示
- ✅ 响应式尺寸调整
- ✅ 智能UI控制（上传时隐藏添加按钮，固定容器尺寸避免布局跳动）

### 样式定制
- ✅ 支持任意尺寸设置
- ✅ 支持多种CSS单位
- ✅ 保持原有样式兼容性

## 注意事项

1. 当传入数字时，会自动添加 `px` 单位
2. 当传入字符串时，会直接使用该字符串作为 CSS 值
3. CSS 变量的使用确保了所有相关的样式元素都会同步调整尺寸
4. 建议在使用时保持宽高比例合理，避免图片变形
5. 上传进度条在小尺寸下可能显示不够清晰，建议使用 60px 以上的尺寸
6. 重试功能需要确保 OSS 配置正确

## 相关文件

- `uploader.vue` - 主组件文件
- `usage-example.vue` - 使用示例和功能演示
- `README.md` - 本说明文档
