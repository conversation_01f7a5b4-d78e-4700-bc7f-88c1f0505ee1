<script setup>
  import { onMounted, ref, watch, computed } from 'vue';
  import { message, message as Message, Upload } from 'ant-design-vue';
  import { aliOssUpload, initAliOss } from '@/utils/alioss';
  import { useOssStore } from '@/store/modules/ossStore';
  import { canPreviewImage, getFileTypeWithUrl, randomId } from '@/utils/tools';
  import ClientImg from '@/components/clientImg/clientImg.vue';
  import Icon from '@/components/icon/icon.vue';
  const pdfIcon = require('@/assets/images/icon_thumb_pdf.png');
  const docIcon = require('@/assets/images/icon_thumb_doc.png');

  const ossStore = useOssStore();
  let ossInstance = ref();
  let uploadList = ref([]); // 上传的文件列表
  let ossInfo = ref({});
  const previewVisible = ref(false);

  const previewImageList = ref([]);
  defineOptions({
    name: 'uploader',
  });
  const emit = defineEmits(['onChange']);
  const props = defineProps({
    fileList: {
      type: Array,
      default: () => [],
    },
    accept: {
      type: String,
      default: 'image/png,image/jpeg,image/jpg',
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    max: {
      type: Number,
      default: 9,
    },
    size: {
      type: Number,
      default: 20,
    },
    preview: {
      type: Boolean,
      default: true,
    },
    needSave: {
      type: Boolean,
      default: true,
    },
    isCustom: {
      // 是否使用自定义上传  自定义上传36*36
      type: Boolean,
      default: false,
    },
    className: {
      type: String,
      default: 'crm-dzpt-upload',
    },
    listType: {
      type: String,
      default: 'picture-card',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    showAddBtn: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [Number, String],
      default: 36,
    },
    height: {
      type: [Number, String],
      default: 36,
    },
  });

  const showAdd = computed(() => {
    // 原有逻辑：文件数量未达到最大值或强制显示添加按钮
    return uploadList.value.length < props.max || props.showAddBtn;
  });

  // 动态计算上传组件的尺寸样式
  const uploadSizeStyle = computed(() => {
    const width = typeof props.width === 'number' ? `${props.width}px` : props.width;
    const height = typeof props.height === 'number' ? `${props.height}px` : props.height;
    return {
      width,
      height,
    };
  });

  /**
   * @description 处理文件类型
   * @param file
   * @returns {*|boolean}
   */
  function handleFileType(file) {
    let fileType = file.type.toLowerCase();
    let lastFileType = `.${getFileTypeWithUrl(file.name)}`;
    lastFileType = lastFileType.toLowerCase();
    if (props.accept === '*') {
      return true;
    } else {
      const acceptList = props.accept.split(',').map((type) => type.trim().toLowerCase());
      const acceptMimes = acceptList.filter((type) => !type.startsWith('.'));
      const acceptExts = acceptList.filter((type) => type.startsWith('.'));
      const isValidMime = acceptMimes.some((mime) => {
        if (mime.includes('/*')) {
          return fileType.startsWith(mime.split('/*')[0]);
        }
        return fileType === mime;
      });
      const isValidExt = acceptExts.some((ext) => {
        return lastFileType === ext;
      });
      return isValidMime || isValidExt;
    }
  }
  /**
   * @description 上传文件之前的钩子
   * @param file
   */
  function beforeUpload(file) {
    let canSelect = handleFileType(file);
    let sizeError = file.size > props.size * 1024 * 1024;
    let maxError = props.max && uploadList.value.length > props.max;
    if (!canSelect) {
      Message.error('文件类型错误');
    }
    if (maxError) {
      Message.error(`最多只能上传${props.max}个文件`);
    }
    if (sizeError) {
      Message.error(`文件大小不能超过${props.size}MB`);
    }
    file.uid = randomId();
    changeFile();
    return (canSelect && !maxError && !sizeError) || Upload.LIST_IGNORE;
  }

  /**
   * @description 上传文件
   * @param e
   */
  function handleUpload(e) {
    let file = e.file;
    e.uid = e.uid || randomId();
    let fileName = ossInfo.value.bucketName + '/' + Date.now() + '-' + e.uid + file.name;
    ossUpload(e, file, fileName);
  }

  /**
   * @description oss上传文件
   * @param e
   * @param file
   * @param fileName
   */
  function ossUpload(e, file, fileName) {
    aliOssUpload(ossInstance.value, file, fileName, ossInfo, (p) => {
      e.onProgress({ percent: p * 100 });
    })
      .then(async (res) => {
        let fileUrl = res.res.requestUrls[0].split('?')[0];
        handleUploadSuccess(e, { url: replaceOssProxyUrl(fileUrl) });
      })
      .catch((err) => {
        handleUploadError(e, err, file);
      });
  }

  /**
   * @description 上传成功
   * @param e
   * @param fileObj
   */
  function handleUploadSuccess(e, fileObj) {
    e.onSuccess(fileObj);
    changeFile();
  }

  /**
   * @description 上传失败
   * @param e
   * @param err
   * @param file
   */
  function handleUploadError(e, err, file) {
    e.onError(err, err);
    message.error(`上传失败-${err.message}`);
    handleDeleteFile(file);
    changeFile();
  }

  /**
   * @description 替换oss代理地址
   * @param url
   * @returns {*}
   */
  function replaceOssProxyUrl(url) {
    let ossInstanceInfo = ossInfo.value;
    let ossProxyUrl = 'https://static-zjsj.haier.net'; // process.env.VUE_APP_OSS_PROXY_HOST;
    let ossUrl = `https://${ossInstanceInfo.bucketName}.oss-${ossInstanceInfo.regionId}.aliyuncs.com`;
    return url.replace(ossUrl, ossProxyUrl);
  }

  /**
   * @description 预览图片
   * @param file
   */
  function handlePreview(file) {
    let lastFileType = `.${getFileTypeWithUrl(file.name)}`;
    lastFileType = lastFileType.toLowerCase();
    if (!canPreviewImage(lastFileType)) {
      if (file.status === 'uploading') {
        Message.error('请等待文件上传后预览');
        return;
      }
      window.open(file.content || file.response.url);
      return;
    }
    let index = uploadList.value.findIndex((item) => {
      return item.uid === file.uid;
    });
    const firstPart = uploadList.value.slice(0, index);
    const secondPart = uploadList.value.slice(index);
    previewImageList.value = [...secondPart, ...firstPart].map((item) => {
      return { ...item, previewUrl: item.response.url };
    });
    previewVisible.value = true;
  }

  /**
   * @description 图片预览显示隐藏
   * @param vis
   */
  function onVisibleChange(vis) {
    previewVisible.value = vis;
  }

  /**
   * @description 创建文件对象 用于文件回显展示
   * @param fileInfo
   * @returns {*}
   */
  function createFileObj(fileInfo) {
    let fileType = getFileTypeWithUrl(fileInfo.name);
    let name = Date.now() + '-' + fileInfo.name; // 文件名超出100进行从前到后截取
    if (name.length > 100 - fileType.length + 1) {
      name = name.substring(0, 100) + '.' + fileType;
    }
    let imgUrl = fileInfo.url || fileInfo.content;
    return {
      ...fileInfo,
      uid: randomId(),
      status: fileInfo.status || 'done',
      response: {
        url: imgUrl,
      },
      thumbUrl: imgUrl,
      fileObj: {
        name: name,
        type: fileType,
        size: fileInfo.size,
      },
    };
  }

  /**
   * @description 文件改变事件
   */
  function changeFile() {
    let isUploading = uploadList.value.some((item) => {
      return item.status === 'uploading';
    });
    uploadList.value.forEach((file) => {
      if (file.status !== 'done') {
        return;
      }
      let lastFileType = `.${getFileTypeWithUrl(file.name)}`;
      lastFileType = lastFileType.toLowerCase();
      if (lastFileType === '.pdf' || lastFileType === '.doc' || lastFileType === '.docx') {
        file.thumbUrl = lastFileType === '.pdf' ? pdfIcon : docIcon;
        file.content = file?.response?.url;
        file.url = file?.response?.url;
      }
    });
    // files: 上传的文件列表   status: true上传全部完成 false 正在上传中
    emit('onChange', { files: uploadList.value, status: !isUploading });
  }

  /**
   * @description 文件改变事件
   */
  function handleChange() {
    changeFile();
  }

  /**
   * @description 获取文件状态
   * @param item
   * @returns {*|string}
   */
  function uploadStatus(item) {
    let map = {
      uploading: 'active',
      done: 'success',
      exception: 'error',
    };
    return map[item.status] || 'normal';
  }

  /**
   * @description 删除文件
   * @param file
   */
  function handleDeleteFile(file) {
    let index = uploadList.value.findIndex((item) => {
      return item.uid === file.uid;
    });
    if (index > -1) {
      uploadList.value.splice(index, 1);
    }
    changeFile();
  }

  /**
   * @description 重试上传
   * @param file
   */
  function retryUpload(file) {
    // 重置文件状态
    file.status = 'uploading';
    file.percent = 0;

    // 重新生成文件名
    let fileName = ossInfo.value.bucketName + '/' + Date.now() + '-' + file.uid + file.name;

    // 创建模拟的上传事件对象
    const mockEvent = {
      file: file,
      uid: file.uid,
      onProgress: (progress) => {
        file.percent = progress.percent;
        changeFile();
      },
      onSuccess: (response) => {
        file.status = 'done';
        file.response = response;
        changeFile();
      },
      onError: (error) => {
        file.status = 'error';
        message.error(`重试上传失败-${error.message}`);
        changeFile();
      },
    };

    // 执行上传
    ossUpload(mockEvent, file, fileName);
  }

  watch(
    () => props.fileList,
    () => {
      uploadList.value = props.fileList.map((item) => {
        return createFileObj(item);
      });
      changeFile();
    },
    {
      immediate: true,
    }
  );

  onMounted(() => {
    ossStore.getOSSInfo().then(async (res) => {
      console.log('获取OSS信息', res);
      ossInfo.value = res;
      ossInstance.value = await initAliOss(ossInfo.value);
    });
  });
</script>

<template>
  <div
    class="uploader-container"
    :style="{ '--upload-width': uploadSizeStyle.width, '--upload-height': uploadSizeStyle.height }">
    <a-upload
      v-model:file-list="uploadList"
      :maxCount="props.max"
      :list-type="listType"
      :class="['crm-dzpt-uploader', isCustom ? 'crm-custom-uploader' : '', props.className]"
      :before-upload="beforeUpload"
      :multiple="props.multiple"
      :customRequest="handleUpload"
      :accept="props.accept"
      :disabled="props.disabled"
      :show-upload-list="props.preview"
      @change="handleChange"
      @preview="handlePreview">
      <template v-if="showAdd">
        <slot name="default">
          <Icon icon="synAdd"></Icon>
        </slot>
      </template>
      <template v-else>
        <slot name="add" />
      </template>
      <template #itemRender="{ file }" v-if="isCustom">
        <div
          class="upload-list-item-content"
          :class="{ uploading: file.status === 'uploading', error: file.status === 'error' }">
          <!-- 上传中状态 -->
          <div v-if="file.status === 'uploading'" class="upload-progress-container">
            <div class="upload-progress-bg"></div>
            <div class="upload-progress-content">
              <a-progress
                type="circle"
                :percent="Number((file.percent || 0).toFixed(0))"
                :strokeWidth="4"
                :status="uploadStatus(file)"
                :size="30"
                :showInfo="false" />
              <div class="upload-progress-text">{{ Number((file.percent || 0).toFixed(0)) }}%</div>
            </div>
            <div class="upload-cancel-btn" @click="handleDeleteFile(file)">
              <Icon icon="synClose" color="#fff" />
            </div>
          </div>

          <!-- 上传完成状态 -->
          <div v-else-if="file.status === 'done'" class="upload-success-container">
            <div class="upload-list-item-content-img" @click="handlePreview(file)">
              <img :src="file.thumbUrl" alt="" />
            </div>
            <div class="delete-image-icon" @click="handleDeleteFile(file)">
              <Icon icon="synClose" />
            </div>
          </div>

          <!-- 上传失败状态 -->
          <div v-else-if="file.status === 'error'" class="upload-error-container">
            <div class="upload-error-content">
              <Icon icon="synWarning" class="error-icon" />
              <div class="error-text">上传失败</div>
            </div>
            <div class="upload-retry-btn" @click="retryUpload(file)">
              <Icon icon="synRefresh" />
            </div>
            <div class="delete-image-icon" @click="handleDeleteFile(file)">
              <Icon icon="synClose" />
            </div>
          </div>
        </div>
      </template>
    </a-upload>
    <slot name="tips">
      <span></span>
    </slot>
    <div class="preview-image" style="display: none">
      <a-image-preview-group
        v-if="previewVisible"
        :preview="{ visible: previewVisible, onVisibleChange: onVisibleChange }">
        <client-img :src="item.previewUrl" v-for="item in previewImageList" :key="item.uid" :ignoreCompress="true" />
      </a-image-preview-group>
    </div>
  </div>
</template>

<style scoped lang="stylus">

  .uploader-container {
    --upload-width: 36px;
    --upload-height: 36px;

    // 为整个容器设置固定尺寸，避免布局跳动
    :deep(.ant-upload-wrapper) {
      .ant-upload-list {
        .ant-upload-select {
          width: var(--upload-width) !important;
          height: var(--upload-height) !important;
          min-width: var(--upload-width) !important;
          min-height: var(--upload-height) !important;
        }
      }
    }
  }

  .select-card{
    width: var(--upload-width) !important;
    height: var(--upload-height) !important;
  }
  .uploader-desc{
    font-size 7px;
  }
  .uploader-add {
    position relative;
    width: 100%;
    height: 100%;
    display flex;
    align-items center;
    justify-content center;
    cursor pointer;
    border-radius 1px;
    border 1px dashed #d9d9d9;
    background #fff;
    transition all 0.3s ease;
    &:hover {
      border-color #40a9ff;
    }
  }


  .cover {
    width: 100%;
    height: 100%;
  }
  .upload-list-item{
    position relative;
    @extends .select-card
    margin 0 8px 8px 0;
  }
  .upload-list-item-content{
    @extends .cover
    position relative
    @extends .select-card
    margin 0 8px 0 0;
    border-radius 1px;
    background rgba(0,0,0,0.35)

    &.uploading {
      background rgba(0,0,0,0.35);
      border 2px solid #1890ff;
    }

    &.error {
      background rgba(255,77,79,0.1);
      border 2px solid #ff4d4f;
    }
  }

  // 上传进度相关样式
  .upload-progress-container {
    @extends .cover
    position relative;
    display flex;
    align-items center;
    justify-content center;
    flex-direction column;
  }

  .upload-progress-bg {
    position absolute;
    top 0;
    left 0;
    @extends .cover
    background rgba(0,0,0,0.35);
    border-radius 1px;
    z-index 1;
  }

  .upload-progress-content {
    position relative;
    z-index 2;
    display flex;
    flex-direction column;
    align-items center;
    justify-content center;
  }

  .upload-progress-text {
    color #fff;
    font-size 10px;
    margin-top 4px;
    font-weight 500;
  }

  .upload-cancel-btn {
    position absolute;
    top -6px;
    right -6px;
    z-index 11;
    width 24px;
    height 24px;
    background rgba(0, 0, 0, 0.45);
    border-radius 50%;
    display flex;
    align-items center;
    justify-content center;
    cursor pointer;
    transition all 0.2s ease;

    &:hover {
      background #ff7875;
      transform scale(1.1);
    }

    :deep(.anticon) {
      color #fff;
      font-size 10px;
    }
  }

  // 上传成功相关样式
  .upload-success-container {
    @extends .cover
    position relative;
  }
  .upload-list-item-content-img {
    cursor pointer;
    position absolute;
    overflow hidden
    z-index 1;
    @extends .cover
    img {
     @extends .cover;
      object-fit cover
    }
  }

  // 上传失败相关样式
  .upload-error-container {
    @extends .cover
    position relative;
    display flex;
    align-items center;
    justify-content center;
    flex-direction column;
  }

  .upload-error-content {
    display flex;
    flex-direction column;
    align-items center;
    justify-content center;
    color #ff4d4f;
  }

  .error-icon {
    font-size 16px;
    margin-bottom 2px;
  }

  .error-text {
    font-size 10px;
    text-align center;
  }

  .upload-retry-btn {
    position absolute;
    bottom -6px;
    left 50%;
    transform translateX(-50%);
    z-index 3;
    width 16px;
    height 16px;
    background #1890ff;
    border-radius 50%;
    display flex;
    align-items center;
    justify-content center;
    cursor pointer;
    transition all 0.2s ease;

    &:hover {
      background #40a9ff;
      transform translateX(-50%) scale(1.1);
    }

    :deep(.anticon) {
      color #fff;
      font-size 10px;
    }
  }
  .ant-upload-wrapper
    :deep(.ant-upload-list-text)
     .ant-upload-list-item-done
      .ant-upload-list-item-name
        @extend .comment-normal
        color var(--opn-color-primary)
  .delete-image-icon{
    position absolute;
    top -6px;
    right -6px;
    z-index 4;
    width 16px;
    height 16px;
    cursor pointer;
    border-radius 50%;
    background #ff4d4f;
    display flex;
    align-items center;
    justify-content center;
    transition all 0.2s ease;

    &:hover {
      background #ff7875;
      transform scale(1.1);
    }

    :deep(.anticon) {
      color #fff;
      font-size 10px;
    }

    // 兼容旧的背景图片方式
    &:before {
      content '';
      background  url( '/public/images/i-upload-delete.png')  0 0 no-repeat ;
      background-size contain
      width 16px
      height 16px
      position absolute
      z-index 1
      display none; // 隐藏旧的背景图片
    }
  }
  .upload-progress{
    position absolute;
    left: 0
    right: 0
    top: 0
    bottom: 0
    z-index 2;
    @extends .cover
    display flex;
    align-items center
    justify-content center
  }

  .crm-custom-uploader {
    :deep(.ant-upload-list) {
      display flex;
      flex-wrap wrap;
      .ant-upload-select{
        @extends .select-card
      }
      .ant-upload-list-item-container{
        @extends .select-card
      }
    }
  }
  :deep(.crm-list-type-uploader)
    background #f00
    .ant-upload-select
      width auto !important
  :deep(.ant-progress.ant-progress-circle) .ant-progress-text{
    color #ffffff;
  }
</style>
<style lang="stylus">
  .crm-dzpt-uploader.ant-upload-wrapper.ant-upload-picture-card-wrapper::after {
    opacity 0;
  }
</style>
