import { computed } from 'vue';
import { message } from 'ant-design-vue';
import { isArray } from '@/utils/isType';

export function useUploader(fileList) {
  /**
   * 是否正在上传
   * @type ComputedRef<*>
   */
  const isUploadIng = computed(() => {
    return fileList.some((item) => {
      return item.status === 'uploading';
    });
  });

  /**
   * 已上传的文件列表
   * @type {ComputedRef<*>}
   */
  const getUploadedFileList = computed(() => {
    return fileList.filter((item) => {
      return item.status === 'success' && item.response;
    });
  });

  /**
   * 创建上传文件
   * @param targetType
   * @returns {*}
   */
  const createUploadFile = (targetType) => {
    let arr = fileList || [];
    if (arr.some((item) => item.status === 'uploading')) {
      message.warning('请等待上传完成');
      return;
    }
    console.log('fileList', arr);
    return arr.map((item) => {
      let params = {
        content: item.response.url,
        name: item.name,
        size: item.size,
        targetType: targetType,
      };
      if (item.response.id) {
        params.id = item.response.id;
      }
      if (item.id) {
        params.id = item.id;
      }
      return params;
    });
  };

  /**
   * 创建上传文件id
   * @param targetType
   */
  const createUploadFileIds = (targetType) => {
    let fileList = createUploadFile(targetType);
    if (isArray(fileList)) {
      return createUploadFile(targetType).map((item) => {
        return item.id;
      });
    }
  };
  return {
    isUploadIng,
    getUploadedFileList,
    createUploadFile,
    createUploadFileIds,
  };
}
