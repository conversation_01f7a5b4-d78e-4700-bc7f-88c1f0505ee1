<template>
  <div class="upload-examples">
    <h3>上传组件功能示例</h3>

    <!-- 默认尺寸 36x36 -->
    <div class="example-section">
      <h4>默认尺寸 (36x36) - 基础功能</h4>
      <uploader
        :fileList="fileList1"
        @onChange="handleChange1"
      />
      <p class="example-desc">支持上传进度显示、失败重试、删除等功能</p>
    </div>

    <!-- 自定义尺寸 60x60 -->
    <div class="example-section">
      <h4>自定义尺寸 (60x60) - 中等尺寸</h4>
      <uploader
        :fileList="fileList2"
        :width="60"
        :height="60"
        @onChange="handleChange2"
      />
      <p class="example-desc">更大的尺寸，进度条和状态显示更清晰</p>
    </div>

    <!-- 自定义尺寸 100x80 -->
    <div class="example-section">
      <h4>自定义尺寸 (100x80) - 大尺寸</h4>
      <uploader
        :fileList="fileList3"
        :width="100"
        :height="80"
        @onChange="handleChange3"
      />
      <p class="example-desc">大尺寸适合展示更多细节，支持不同宽高比</p>
    </div>

    <!-- 使用字符串单位 -->
    <div class="example-section">
      <h4>使用字符串单位 (5rem x 4rem)</h4>
      <uploader
        :fileList="fileList4"
        width="5rem"
        height="4rem"
        @onChange="handleChange4"
      />
      <p class="example-desc">支持rem、em、%等CSS单位</p>
    </div>

    <!-- 模拟上传状态 -->
    <div class="example-section">
      <h4>上传状态演示 - 智能添加按钮</h4>
      <div class="demo-buttons">
        <a-button @click="simulateUpload" type="primary">模拟上传</a-button>
        <a-button @click="simulateError" danger>模拟失败</a-button>
        <a-button @click="clearFiles">清空文件</a-button>
      </div>
      <uploader
        :fileList="fileList5"
        :width="80"
        :height="80"
        @onChange="handleChange5"
      />
      <p class="example-desc">
        <strong>智能功能：</strong>当有文件正在上传时，添加按钮会自动隐藏。通过固定容器尺寸避免布局跳动问题
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Uploader from './uploader.vue'

const fileList1 = ref([])
const fileList2 = ref([])
const fileList3 = ref([])
const fileList4 = ref([])
const fileList5 = ref([])

const handleChange1 = (data) => {
  console.log('默认尺寸上传变化:', data)
}

const handleChange2 = (data) => {
  console.log('60x60尺寸上传变化:', data)
}

const handleChange3 = (data) => {
  console.log('100x80尺寸上传变化:', data)
}

const handleChange4 = (data) => {
  console.log('5rem x 4rem尺寸上传变化:', data)
}

const handleChange5 = (data) => {
  console.log('演示上传变化:', data)
  fileList5.value = data.files
}

// 模拟上传功能
const simulateUpload = () => {
  const mockFile = {
    uid: Date.now(),
    name: 'demo-image.jpg',
    status: 'uploading',
    percent: 0,
    url: 'https://via.placeholder.com/100x100',
    thumbUrl: 'https://via.placeholder.com/100x100'
  }

  fileList5.value.push(mockFile)

  // 模拟上传进度
  let progress = 0
  const timer = setInterval(() => {
    progress += Math.random() * 20
    if (progress >= 100) {
      progress = 100
      mockFile.status = 'done'
      mockFile.percent = 100
      clearInterval(timer)
    } else {
      mockFile.percent = progress
    }
  }, 200)
}

const simulateError = () => {
  const mockFile = {
    uid: Date.now(),
    name: 'error-file.jpg',
    status: 'error',
    percent: 0,
    url: '',
    thumbUrl: ''
  }

  fileList5.value.push(mockFile)
}

const clearFiles = () => {
  fileList5.value = []
}
</script>

<style scoped>
.upload-examples {
  padding: 20px;
}

.example-section {
  margin-bottom: 30px;
  padding: 15px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.example-section h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
}

.example-desc {
  margin-top: 10px;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.demo-buttons {
  margin-bottom: 15px;
}

.demo-buttons .ant-btn {
  margin-right: 8px;
}
</style>
