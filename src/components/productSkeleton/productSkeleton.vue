<template>
  <div class="skeleton">
    <a-skeleton-image v-if="props.showImage"/>
    <div v-for="s in Array.from({ length: props.skeletonLength }, (_, i) => i + 1)" :key="s">
      <a-skeleton active />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

const props = defineProps({
  showImage: {
    type: Boolean,
    default: true,
  },
  skeletonLength: {
    type: Number,
    default: 1
  }
})

onMounted(() => {

})

</script>

<style scoped lang="stylus">
  .skeleton {
    width 100%;
    height 100%;
  }
</style>
