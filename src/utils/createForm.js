import { checkNumberInput } from '@/utils/tools';
export const MAX_NUMBER = 999999999;
export const ITEM_WIDTH = '240px';

export function initFormConfig(config) {
  let formData = {};
  let formItems = Object.entries(config).map((i) => {
    const [key, item] = i;
    const formItem = {
      ...item,
      name: key,
    };
    let dItem = '';

    if (item.is === 'a-select') {
      dItem = void 0;
    }

    if (item.is === 'a-range-picker') {
      dItem = [];
    }

    if (item.dataType) {
      dItem = item.dataType;
    }
    formData[key] = dItem;

    return formItem;
  });

  return { formData, formItems };
}

export function checkboxGroupFormItem(config) {
  return {
    props: config,
    rules: generatorRulesConfig(config),
  };
}

export function radioGroupGroupFormItem(config) {
  return {
    props: config,
    rules: generatorRulesConfig(config),
  };
}

export function inputFormItem(config) {
  config.style = {
    ...config.style,
    width: config?.style?.width || ITEM_WIDTH,
  };
  return { props: generatorInput(config), rules: generatorRulesConfig(config) };
}

export function datePickerFormItem(config = {}) {
  config.style = {
    ...config.style,
    width: config?.style?.width || ITEM_WIDTH,
  };
  return { props: generatorDatePicker(config), rules: generatorRulesConfig(config) };
}

export function customComponentFormItem(config) {
  return { props: config, rules: generatorRulesConfig(config) };
}

export function generatorInput(inputItem) {
  return {
    placeholder: '请输入',
    allowClear: true,
    showWordLimit: false,
    maxlength: 100,
    disabled: false,
    readonly: false,
    ...inputItem,
  };
}
export function generatorTextarea(textareaItem) {
  return {
    placeholder: '请输入',
    rows: 2,
    allowClear: true,
    showWordLimit: true,
    maxlength: 200,
    showPassword: false,
    prefixIcon: '',
    suffixIcon: '',
    disabled: false,
    readonly: false,
    ...textareaItem,
  };
}
export function generatorSelect(selectItem) {
  return {
    placeholder: '请选择',
    allowClear: true,
    multiple: false,
    disabled: false,
    collapseTags: false,
    collapseTagsTooltip: false,
    showSearch: true,
    showArrow: true,
    ...selectItem,
  };
}
export function generatorRadio(radioItem) {
  return {
    border: false,
    disabled: false,
    size: 'medium',
    button: false,
    fill: false,
    minWidth: 0,
    options: [],
    ...radioItem,
  };
}
export function generatorCheckbox(checkboxItem) {
  return {
    border: false,
    disabled: false,
    size: 'medium',
    minWidth: 0,
    ...checkboxItem,
  };
}
export function generatorDatePicker(datePickerItem) {
  let format = datePickerItem.showTime ? 'YYYY-MM-DD HH:mm' : 'YYYY-MM-DD';
  return {
    format,
    valueFormat: format,
    placeholder: '请选择时间',
    ...datePickerItem,
  };
}
export function generatorDateRangePicker(datePickerItem) {
  return {
    ...generatorDatePicker({ ...datePickerItem, placeholder: ['开始时间', '结束时间'] }),
  };
}
export function generatorTimePicker(timePickerItem) {
  return {
    type: 'time-picker',
    format: 'HH:mm:ss',
    valueFormat: 'HH:mm:ss',
    placeholder: '请选择时间',
    ...timePickerItem,
  };
}
export function generatorCascader(cascaderItem) {
  return {
    type: 'cascader',
    placeholder: '请选择',
    allowClear: true,
    multiple: false,
    disabled: false,
    collapseTags: false,
    collapseTagsTooltip: false,
    filterable: false,
    options: [],
    ...cascaderItem,
  };
}
export function generatorSwitch(switchItem) {
  return {
    type: 'switch',
    activeText: '开',
    inactiveText: '关',
    activeValue: true,
    inactiveValue: false,
    disabled: false,
    ...switchItem,
  };
}
export function generatorInputNumber(inputNumberItem) {
  return {
    type: 'input-number',
    placeholder: '请输入',
    allowClear: true,
    max: MAX_NUMBER,
    maxlength: inputNumberItem.maxlength || 100,
    disabled: false,
    readonly: false,
    ...inputNumberItem,
  };
}
export const generatorMap = {
  input: generatorInput,
  select: generatorSelect,
  radio: generatorRadio,
  checkbox: generatorCheckbox,
  dateRangePicker: generatorDateRangePicker,
  datePicker: generatorDatePicker,
  timePicker: generatorTimePicker,
  cascader: generatorCascader,
  switch: generatorSwitch,
  inputNumber: generatorInputNumber,
};

// 生成 校验规则
export function generatorRulesConfig(config) {
  if (!Array.isArray(config.rules) || config.rules.length === 0) {
    return [];
  }
  let rules = [];
  config.rules.forEach((i) => {
    if (i === 'blur&change') {
      rules.push({
        required: false,
        message: config.placeholder,
        trigger: ['blur', 'change'],
      });
    }
    if (i === 'changeRequired') {
      rules.push({
        required: true,
        message: config.placeholder,
        trigger: 'change',
      });
    }
    if (i === 'blurRequired') {
      rules.push({
        required: true,
        message: config.placeholder,
        trigger: 'blur',
      });
    }
    if (i === 'blur&changeRequired') {
      rules.push({
        required: true,
        message: config.placeholder,
        trigger: ['blur', 'change'],
      });
    }
    if (i === 'required') {
      rules.push({
        required: true,
        message: config.placeholder,
        trigger: 'blur',
      });
    }
    if (i === 'isPhone') {
      rules.push({
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号',
        trigger: 'blur',
      });
    }
    if (i === 'isUpload') {
      rules.push({
        pattern: /^1[3-9]\d{9}$/,
        message: '请输入正确的手机号',
        trigger: 'blur',
      });
    }
    if (i === 'isMoney') {
      rules.push({
        validator: checkNumberInput,
        trigger: 'change',
      });
    }
  });
  return rules;
}

export function selectFormItem(config) {
  config.style = {
    ...config.style,
    width: config?.style?.width || ITEM_WIDTH,
  };
  config.maxTagCount = 1;
  config.mode = config.multiple;
  config.maxTagTextLength = 6;

  return { props: generatorSelect(config), rules: generatorRulesConfig(config) };
}
