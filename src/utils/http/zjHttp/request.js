import Axios from 'axios';
import { getMainPlatformHeaders, handleError } from '@/utils/http/request.config.js';
import { message } from 'ant-design-vue';

// 创建 axios 实例
const http = Axios.create({
  baseURL: process.env.VUE_APP_ZJ_API_URL,
  timeout: 30000, // 请求超时时间
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    let headers = getMainPlatformHeaders();
    config.headers = {
      'X-Access-Token': headers['X-Access-Token'],
      'X-Refresh-Token': headers['X-Refresh-Token'],
      ...config.headers,
    };
    // 取消重复请求
    // cancelRepeatRequest(config)
    // 添加请求到队列
    // addPendingRequest(config)
    return config;
  },
  (error) => {
    message.error(error.message);
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    if (response.status === 200 && response?.data?.retCode === '00000') {
      return response.data;
    } else {
      message.error({
        content: response?.data?.retInfo || '系统繁忙请稍后重试',
        duration: 3000,
      });
    }
  },
  (error) => {
    console.log('error', error);
    if (error.response) {
      handleError(error);
    }
    // 取消请求队列中该请求
    // cancelRepeatRequest(error.config || {})
    return Promise.reject(error);
  }
);

export { http };
