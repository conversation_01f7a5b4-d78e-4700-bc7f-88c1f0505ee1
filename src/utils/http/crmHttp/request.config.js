import { useUserStore } from '@/store/modules/user';
import { message, Modal } from 'ant-design-vue';
import { isQianKun } from '@/utils/isEnv';
import mainPlatformApi from '@/utils/mainPlatformTools';
import { STATIC_STORE_CODE, TOKEN_INFO } from '@/config/devConfig';

let modalInstance = null;
const SERVICE_URL_ENUM = {
  sys: 'store-base',
  crm: 'store-crm',
  oa: 'store-oa',
  bi: 'store-bi',
  cms: 'store-cms',
  qywx: 'store-qywx',
  api: 'store-crm',
};

/**
 * 将原有crm的微服务地址前缀改为三翼鸟新服务前缀
 * @param url
 * @returns {string|*}
 */
export function escapeUrl(url) {
  if (!url) {
    return '';
  }
  let escapeUrl = '';
  for (const [key, value] of Object.entries(SERVICE_URL_ENUM)) {
    if (url.startsWith(key) || url.startsWith(`/${key}`)) {
      escapeUrl = url.replace(key, `${value}/${key}`);
      break;
    }
  }
  return escapeUrl;
}

export function handleError(error) {
  const data = error?.response?.data;
  if (!data) {
    return;
  }
  switch (error.response.status) {
    case 403:
      message.error(`拒绝访问`);
      break;
    case 500:
      retryGetToken(data);
      break;
    case 404:
      message.error(`很抱歉，资源未找到!`);
      break;
    case 504:
      message.error(`网络超时`);
      break;
    case 401:
      handleTokenExpired(data);
      break;
    case 444:
      message.error(`未知错误`);
      break;
    default:
      message.error(`未知错误`);
      break;
  }
}

function retryGetToken(data) {
  Modal.confirm({
    title: '提示',
    content: data.message,
    okText: '确定',
    cancelText: '取消',
    onOk() {
      console.log('OK');
      if (isQianKun()) {
        mainPlatformApi.portalReLogin();
      }
    },
    onCancel() {
      console.log('Cancel');
    },
    class: 'test',
  });
}

function handleTokenExpired(data) {
  if (modalInstance) {
    return;
  }
  modalInstance = Modal.confirm({
    title: '提示',
    content: `登录已过期，请重新登录，${data.message}`,
    okText: '确定',
    cancelText: '取消',
    onOk() {
      modalInstance.destroy();
      if (isQianKun()) {
        mainPlatformApi.portalReLogin();
      }
    },
    onCancel() {
      modalInstance.destroy();
      console.log('Cancel');
    },
  });
}

/**
 * 获取携带的token
 * @returns {{'X-Access-Token': (*|string), 'X-Refresh-Token': (*|string)}}
 */
export function getHeaderToken() {
  if (isQianKun()) {
    let mainToken = mainPlatformApi.getToken();
    let userInfo = mainPlatformApi.getUserInfo();
    return {
      'Third-Access-Token': mainToken?.token || '',
      'Third-Refresh-Token': mainToken?.refreshToken || '',
      'Third-Expires-In': mainToken.expires_in || '3600',
      'Store-Code': userInfo.tenantId,
      'Token-Source': 'DZPT',
    };
  } else {
    return {
      'Third-Access-Token': TOKEN_INFO['gzt_access_token'],
      'Third-Refresh-Token': TOKEN_INFO['gzt_refresh_access_token'],
      'Third-Expires-In': TOKEN_INFO['expires_in'],
      'Store-Code': STATIC_STORE_CODE,
      'Token-Source': 'DZPT',
    };
  }
}

/**
 * 设置请求头
 * @returns {{}}
 */
function setHeader() {
  let headers = {};
  const userStore = useUserStore();
  let { userInfo } = userStore;
  headers.tenantid = userInfo.accountId || -1;
  //客户端类型 （0：PC，1：H5，2：安卓，3：IOS）
  headers.clienttype = 1;
  headers = {
    ...headers,
    ...getHeaderToken(),
  };
  return headers;
}

export function getHeaders() {
  return setHeader();
}
