import Axios from 'axios';
import { message } from 'ant-design-vue';
import { escapeUrl, getHeaders, handleError } from '@/utils/http/crmHttp/request.config.js';
import signMd5Utils from '@/utils/encryption/signMd5Utils.js'; // 默认 axios 实例请求配置
import { Modal } from 'ant-design-vue';
import mainPlatformApi from '@/utils/mainPlatformTools';

// 创建 axios 实例
const http = Axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 60000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    withCredentials: true,
  },
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    const { url, method, params, data } = config;
    const headers = getHeaders();
    config.headers = {
      ...headers,
      ...config.headers,
      ...{
        'X-Sign': signMd5Utils.getSign(url, method.toLocaleLowerCase() === 'get' ? params : data),
        'X-TIMESTAMP': signMd5Utils.getDateTimeToString(),
        // 'X-Syn-Gray-Tag': true,
        // 'X-Server-Green-Blue-tag': true,
      },
    };
    //TODO 可能不再需要 escapeUrl ,新接口 api 开头
    config.url = escapeUrl(config.url);
    if (config.method.toLocaleLowerCase() !== 'post') {
      config.params = config.data;
    }
    if (config.method.toLocaleLowerCase() === 'get') {
      if (config.params) {
        config.params._tt = new Date().getTime();
      }
    }
    return config;
  },
  (error) => {
    message.error(error.message);
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    // 检查状态码是否为 200 且 code 字段为 500 message该用户不存在 且接口是有/getCurrentUserInfo
    if (
      response.status === 200 &&
      response.data.code === 500 &&
      response.request.responseURL.includes('/getCurrentUserInfo')
    ) {
      if (response.data.message === '人员信息不存在!') {
        Modal.confirm({
          title: '提示',
          content: `当前账号暂无用户管理权限`,
          closable: true,
          footer: null,
          bodyStyle: {
            paddingTop: '20px',
            paddingBottom: '40px',
          },
          onCancel() {
            console.log('Cancel');
            mainPlatformApi.goToPage({ name: 'home' });
          },
        });
      } else {
        Modal.confirm({
          title: '提示',
          content: `登录已过期，请重新登录，${response.data.message}`,
          okText: '确定',
          cancelText: '取消',
          onOk() {
            mainPlatformApi.portalReLogin();
          },
          onCancel() {
            console.log('Cancel');
          },
        });
      }
      // return Promise.reject(response.data.message);
      return response.data;
    }
    return response.data;
  },
  (error) => {
    if (error.code === 'ERR_CANCELED') {
      return Promise.reject({ status: 499 });
    }
    if (error.response) {
      handleError(error);
    }
    return Promise.reject(error);
  }
);

export { http };
