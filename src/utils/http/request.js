import Axios from 'axios';
import { getHeaders, getMainPlatformHeaders, handleError, handleResponse } from './request.config';

// 创建 axios 实例
const http = Axios.create({
  baseURL: process.env.VUE_APP_URL,
  timeout: 30000, // 请求超时时间
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    let url = config.url?.url || config.url;
    let headers = getHeaders();
    if (url && (url.startsWith('/api/syn-base-authorization') || url.startsWith('/api/syn-store-construct'))) {
      config.baseURL = process.env.VUE_APP_DZPT_REQUEST_URL;
      headers = getMainPlatformHeaders();
    }
    config.headers = {
      ...headers,
      ...config.headers,
      // TODO... 联调测试环境时需要删除此部分的修改
      'x-env': 'dev',
    };
    if (config.method.toLocaleLowerCase() === 'get') {
      config.params = config.data;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    return handleResponse(response);
  },
  (error) => {
    if (error.response) {
      handleError(error);
    }
    return Promise.reject(error);
  }
);
export { http };
