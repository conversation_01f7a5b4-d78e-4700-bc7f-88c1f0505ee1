import { message, Modal } from 'ant-design-vue';
import { useUserStore } from '@/store/modules/userStore';
import { MAIN_PLATFORM_REQUEST_URLS, PLATFORM } from '@/config/const';
import { goToLogin } from '@/utils/gotoPage';

let confirm401 = false;

/**
 * 处理错误事件
 * @param {object} error
 * @returns
 */
export function handleError(error) {
  switch (error.response.status) {
    case 404:
      message.error(`接口不存在${error.config.url}`);
      break;
    case 401:
      if (confirm401) {
        return Promise.reject();
      }
      Modal.destroyAll();
      Modal.confirm({
        title: '提示',
        content: '登录已过期，请重新登录',
        okText: '确定',
        cancelText: '取消',
        onOk() {
          goToLogin();
          confirm401 = false;
        },
        onCancel() {
          confirm401 = false;
          console.log('Cancel');
        },
      });
      confirm401 = true;
      break;
    default:
      const data = error?.response?.data;
      message.error(data.message);
      break;
  }
}

/**
 * 处理返回事件
 * @param {object} response
 * @returns
 */
export function handleResponse(response) {
  const url = response.config.url;
  let codeList = [0, -2, -3, 1, '0', '2'];
  if (
    codeList.includes(response?.data?.code) ||
    codeList.includes(response?.data?.status) ||
    (response.data?.code === 200 && MAIN_PLATFORM_REQUEST_URLS.includes(url))
  ) {
    return response.data;
  } else {
    let ignoreErrorApiList = [
      '/by_t/',
      '/iam/getTokenAndUserInfo',
      '/register/verifyEmailOrNo',
      '/zjsj/haierhome/user/getRetailStore',
      '/erp/order/v1/management/submit',
    ];
    let isIgnoreError = ignoreErrorApiList.some((item) => {
      return url.includes(item);
    });
    if (isIgnoreError || response.config.ignoreError) {
      return response.data;
    }
    const res = response.data;

    message.error(res.msg ? res.msg : res.data ? res.data : '系统繁忙请稍后重试');
    return response.data;
  }
}

/**
 * 获取携带的token
 * @returns {{'X-Access-Token': (*|string), 'X-Refresh-Token': (*|string)}}
 */
export function getHeaderToken() {
  const userStore = useUserStore();
  return {
    'X-Access-Token': userStore.token,
    'X-Refresh-Token': userStore.refresh_token,
    Authorization: userStore.token,
  };
}

/**
 * 设置请求头
 * @returns
 */
export function getHeaders() {
  let tokenInfo = getHeaderToken();
  return {
    'X-Access-Token': tokenInfo['X-Access-Token'],
    'X-Refresh-Token': tokenInfo['X-Refresh-Token'],
    Authorization: tokenInfo['Authorization'],
    platform: PLATFORM,
  };
}

export function getMainPlatformHeaders() {
  let tokenInfo = getHeaderToken();
  return {
    'X-Access-Token': tokenInfo['X-Access-Token'],
    'X-Refresh-Token': tokenInfo['X-Refresh-Token'],
    'x-app-id': 'DZPT-GZT',
  };
}
