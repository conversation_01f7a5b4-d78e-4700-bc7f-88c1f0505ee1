import { formatDateString } from '@/utils/tools';
import { getOSSToken } from '@/api/comm.api';
const OSS = require('ali-oss');
const showFailToast = () => {}; // todo

/**
 * 初始化阿里云oss
 * @param ossInfo
 * @returns {Promise<unknown>}
 */
export async function initAliOss(ossInfo) {
  let aliOssParams = createOssParams(ossInfo);
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      console.log('阿里云oss参数', aliOssParams);
      let clientOss = new OSS(aliOssParams);
      resolve(clientOss);
    } catch (e) {
      reject(e);
    }
  });
}

function createOssParams(ossInfo) {
  let dateStr = formatDateString(ossInfo.expiration);
  return {
    region: `oss-${ossInfo.regionId || 'cn-qingdao'}`,
    accessKeyId: ossInfo.accessKeyId,
    accessKeySecret: ossInfo.accessKeySecret,
    secure: true,
    bucket: ossInfo.bucketName,
    stsToken: ossInfo.securityToken,
    refreshSTSTokenInterval: new Date(dateStr).getTime() - new Date().getTime(),
    refreshSTSToken: async () => {
      const info = await getOSSToken();
      if (info?.code === 0 && info?.success) {
        return {
          accessKeyId: info?.result?.accessKeyId,
          accessKeySecret: info?.result?.accessKeySecret,
          stsToken: info?.result?.securityToken,
        };
      }
    },
  };
}

/**
 * @description ali-oss上传
 * @param ossInstance
 * @param file
 * @param fileName
 * @param ossInfo
 * @param progressCallback
 * @returns {Promise<unknown>}
 */
export function aliOssUpload(ossInstance, file, fileName, ossInfo, progressCallback = () => {}) {
  return new Promise((resolve, reject) => {
    ossInstance
      .multipartUpload(fileName, file, {
        progress: (p, info) => {
          progressCallback(p, info);
        },
      })
      .then((res) => {
        resolve(res);
      })
      .catch((err) => {
        reject(err);
        showFailToast('文件上传失败');
      });
  });
}
