export const ua = window.navigator.userAgent.toLowerCase();

// // 微信生态
// export const isWechat = (() => {
//   return /MicroMessenger/i.test(ua);
// })();
//
// // 微信小程序
// export const isWxmp = (() => {
//   return (/MicroMessenger/i.test(ua) && /miniProgram/i.test(ua)) || window.__wxjs_environment === 'miniprogram';
// })();
//
// // 百度小程序平台
// export const isBaiduMp = (() => {
//   return /swan\//.test(ua) || /^webswan-/.test(window.name);
// })();
//
// // 支付宝小程序
// export const isAlipaymp = (() => {
//   return /Alipay/i.test(ua);
// })();
//
// // 微博
// export const isWeibo = (() => {
//   return /WeiBo/i.test(ua);
// })();
//
// // QQ
// export const isQQ = (() => {
//   return /QQ/i.test(ua);
// })();

/**
 * 判断是否是定制平台--乾坤微应用
 * @returns {boolean}
 */
export function isQianKun() {
  return window.__IS_DZPT_QIANKUN__;
}

/**
 * 判断是否是iframe
 * @returns {boolean}
 */
export function isIframe() {
  return window.self !== window.parent;
}
export const isProd = () => process.env.VUE_APP_ENV === 'prod';

export const isDev = () => process.env.VUE_APP_ENV === 'dev';

export const isTest = () => process.env.VUE_APP_ENV === 'test';
