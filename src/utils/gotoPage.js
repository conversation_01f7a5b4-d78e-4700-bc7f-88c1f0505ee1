/**
 * 导航到登录页面的函数
 *
 * 该函数根据提供的查询参数和类型，生成登录URL并导航到该页面
 * 查询参数中，特殊的redirect参数会被进行URL编码，以确保安全性
 * 根据类型的不同，采取不同的导航方式：新窗口打开、在同一窗口中导航或替换当前历史记录
 *
 * @param {Object} query - 查询参数对象，包含要添加到URL的参数 键值对
 * @param {Object} query.redirect - 重定向地址，登录成功后跳转的地址，如果为空，默认无重定向，注意该参数会被进行URL编码，以防止XSS攻击
 * @param {number} type - 导航类型 1: 新窗口打开 2: 同一窗口中导航 默认: 替换当前历史记录
 */
export function goToLogin(query = {}, type = 3) {
  let { origin } = window.location;
  const LOGIN_URL = `${origin}/console/login`;
  let url = new URL(LOGIN_URL);

  for (let key in query) {
    // 只有当查询参数的值存在时，才将其添加到URL中
    if (query[key]) {
      // 如果key值为redirect，则使用encodeURIComponent进行编码
      if (key === 'redirect') {
        // 对redirect参数进行URL编码，防止XSS攻击
        url.searchParams.set(key, encodeURIComponent(query[key]));
      } else {
        // 其他参数直接添加到URL中
        url.searchParams.set(key, query[key]);
      }
    }
  }

  // 根据type参数的不同，选择不同的导航方式
  switch (type) {
    case 1:
      // 在新窗口中打开登录页面
      window.open(url);
      break;

    case 2:
      // 在当前窗口中导航到登录页面
      window.location.assign(url);
      break;
    case 3:
      window.location.replace(url);
      break;
    default:
      // 替换当前历史记录，适用于不想让用户通过后退按钮返回到前一个页面的场景
      window.location.replace(url);
      break;
  }
}
