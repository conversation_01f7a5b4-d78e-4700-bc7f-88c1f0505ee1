import { gioData } from './gioData.js';
import { generateDateTimeWithT } from '@/utils/tools';
import { useUserStore } from '@/store/modules/user';
// import { isDev } from '@/utils/isEnv';
// import { STATIC_STORE_CODE } from '@/config/devConfig';
// import mainPlatformApi from '@/utils/mainPlatformTools';

const getGioTrackData = () => {
  const userStore = useUserStore();
  const COM_GIO_TEMPLATE = {};
  COM_GIO_TEMPLATE.operator = userStore?.userInfo?.ucId;

  const GIO_DATA = gioData;
  for (const i in GIO_DATA) {
    if (Object.hasOwn(GIO_DATA[i], 'd')) {
      GIO_DATA[i].d = Object.assign(GIO_DATA[i].d, COM_GIO_TEMPLATE);
    }
  }
  return GIO_DATA;
};

export async function gioTrackReport(id, payload = {}) {
  const data = getGioTrackData();
  let gioItem = data[id];
  if (!gioItem) {
    return;
  }
  if (payload.entry_time) {
    data[id].d.entry_time = generateDateTimeWithT(payload.entry_time);
  }

  if (payload.value_name) {
    data[id].d.value_name = payload.value_name;
  }

  if (Object.hasOwn(payload, 'leave_time')) {
    data[id].d.leave_time = payload.leave_time ? generateDateTimeWithT(payload.leave_time) : '';
  }
  if (data[id].d.BusinessoppID === '') {
    data[id].d.BusinessoppID = payload.chanceId || '';
  }

  if (Object.hasOwn(data[id].d, 'leave_time') || Object.hasOwn(data[id].d, 'entry_time')) {
    delete data[id].d.time;
  }

  if (payload.Reminder_code) {
    data[id].d.Reminder_code = payload.Reminder_code;
  }
  if (payload.content_type) {
    data[id].d.content_type = payload.content_type;
  }
  console.log('GIO_TRACK', id, data[id].d);
  window.gio('track', id, data[id].d);
}

const GIO_TRACK = {
  gioTrackReport,
};
export default GIO_TRACK;
