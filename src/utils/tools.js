import { isTruly, isFunction, isInvalidDate, isNumber } from '@/utils/isType';
import { isIntOrTwoDecimal } from '@/utils/validate';
import dayjs from 'dayjs';

export function copyText(text) {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    if (text) {
      try {
        await navigator.clipboard.writeText(text);
        resolve({ text, message: '复制成功' });
      } catch (err) {
        reject({ text: text, message: '复制失败', err });
      }
    } else {
      reject({ text: null, messags: '复制失败内容不可为空' });
    }
  });
}

export function injectStyle(css, { insertAt } = {}) {
  if (!css || typeof document === 'undefined') {
    return;
  }
  const head = document.head || document.getElementsByTagName('head')[0];
  const style = document.createElement('style');
  style.id = randomId();
  if (insertAt === 'top') {
    if (head.firstChild) {
      head.insertBefore(style, head.firstChild);
    } else {
      head.appendChild(style);
    }
  } else {
    head.appendChild(style);
  }

  if (style.styleSheet) {
    style.styleSheet.cssText = css;
  } else {
    style.appendChild(document.createTextNode(css));
  }
}
/**
 * @description 获取文件类型
 * @param url
 * @returns {string}
 */
export function getFileTypeWithUrl(url) {
  if (!url) {
    return '';
  }
  const length = url.lastIndexOf('?');
  let fileUrl = '';
  if (length > -1) {
    fileUrl = url.substr(0, length); //文件最终路径
  } else {
    fileUrl = url;
  }
  return fileUrl.substring(fileUrl.lastIndexOf('.') + 1, fileUrl.length);
}

/**
 * @description 判断图片是否可以预览
 * @param url
 * @returns {boolean}
 */
export function canPreviewImage(url) {
  let fileType = getFileTypeWithUrl(url);
  return ['gif', 'png', 'jpg', 'jpeg', 'bmp', 'webp', 'jfif'].includes(fileType.toLowerCase());
}

/**
 * @description 格式化时间字符串  针对OSS返回的时间格式进行处理 2023-07-07 09:07:07:0123
 * @param dateString
 * @returns {*}
 */
export function formatDateString(dateString) {
  let time = dateString.split(':');
  if (time.length === 3) {
    return dateString.replace(/-/g, '/');
  }
  if (time.length === 4) {
    const dateWithoutMilliseconds = dateString.split(':').slice(0, -1).join(':');
    return dateWithoutMilliseconds.replace(/-/g, '/');
  }
  return dateString;
}

/**
 * @description 生成固定格式的时间
 * @param time
 * @returns {string}
 */
export function generateDateTimeWithT(time) {
  const now = new Date(time);

  const year = now.getFullYear();
  const month = ('0' + (now.getMonth() + 1)).slice(-2); // 月份从 0 开始，所以加 1
  const day = ('0' + now.getDate()).slice(-2);
  const hours = ('0' + now.getHours()).slice(-2);
  const minutes = ('0' + now.getMinutes()).slice(-2);
  const seconds = ('0' + now.getSeconds()).slice(-2);

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}`;
}

/**
 * 获取url上的参数
 * @param name
 * @param url
 * @returns {null|string}
 */
export function getQueryString(name, url = window.location.href) {
  const reg = new RegExp('[?&]' + name + '=([^&]+)', 'g');
  const urlData = reg.exec(url);
  if (urlData !== null) {
    return urlData[1];
  }
  return null;
}

/**
 * @description: 解析url参数
 * @param url
 * @returns {{}}
 */
export function getQueryParams(url) {
  const params = {};
  const [baseUrl, hash] = url.split('#');

  // 处理查询参数
  const queryString = baseUrl.split('?')[1];
  if (queryString) {
    queryString.split('&').forEach((param) => {
      const [key, value] = param.split('=');
      params[decodeURIComponent(key)] = decodeURIComponent(value || '');
    });
  }

  // 处理哈希部分的查询参数
  if (hash) {
    const hashParams = hash.split('?')[1];
    if (hashParams) {
      hashParams.split('&').forEach((param) => {
        const [key, value] = param.split('=');
        params[decodeURIComponent(key)] = decodeURIComponent(value || '');
      });
    }
  }
  return params;
}

export function randomId() {
  return Math.random().toString(36).substring(4, 12);
}
// 取名字的第一个字
export function splitFirstFilter(str) {
  if (!str) {
    return str;
  }
  return str.substring(0, 1);
}

/**
 * 过滤对象中为空的属性
 * @param obj
 * @returns {*}
 */

export function filterObj(obj) {
  if (typeof obj !== 'object') {
    return obj;
  }
  for (const key in obj) {
    if (!obj[key]) {
      delete obj[key];
    }
    if (typeof obj[key] === 'string' && obj[key].indexOf('*') === 0 && obj[key].lastIndexOf('*', obj[key].length - 1)) {
      const encodeList = ['[', ']'];
      const arr = obj[key].substring(1, obj[key].length - 1).split('');
      const res = arr.map((i) => {
        let str = '';
        if (encodeList.includes(i)) {
          str = encodeURI(i);
        } else {
          str = i;
        }
        return str;
      });
      obj[key] = `*${res.join('')}*`;
    }
  }
  return obj;
}
import { isString } from '@/utils/isType';
import { CARD_IMG_COMPRESS_PARAMS, PREVIEW_IMG_COMPRESS_PARAMS } from '@/config/const';

export const COLOR_THEME = {
  desc: '#00BCB7', //名词属性
  process: '#5FB84F', // 进行中、过程中
  error: '#FF2121', //警示、驳回、延期、超时
  warning: '#FF9533', //待进行、未开始状态
  confirm: '#4574F5', //确认、已完成
  cancel: '#666666', //结束、取消、已关闭
};

const DICT_COLS = {
  chanceTextColor: {
    3: {
      color: COLOR_THEME.cancel,
      icon: '',
      status: ['60'],
    }, // 已取消
    2: {
      color: COLOR_THEME.confirm,
      icon: 'i-selected-12',
      status: ['50'],
    }, // 已完成
    1: {
      color: COLOR_THEME.process,
      icon: 'i-follow-ing',
      status: ['21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '40'],
    }, // 进行中
    0: {
      color: COLOR_THEME.warning,
      icon: '',
      status: ['0'],
    }, // 未开始
  },
  chanceType: {
    0: 'primary',
    1: 'success',
    2: 'danger',
    default: 'default',
  },
  appliance: {
    1: '成套家电',
    2: '前置家电',
  },

  building: {
    3: COLOR_THEME.cancel,
    2: COLOR_THEME.confirm,
    0: COLOR_THEME.warning,
    1: COLOR_THEME.process,
    default: COLOR_THEME.cancel,
  },
  orderType: {
    0: 'default',
    1: 'warning',
    2: 'success',
    3: 'danger',
    4: 'default',
  },
};

/**
 * @description 过滤器转化
 * @param {String} val
 * @param {String} key
 * @return {String}
 */
export function transformFilter(val, key) {
  let text = '';
  if (!key) {
    return text;
  }
  if (DICT_COLS[key] && DICT_COLS[key][val]) {
    text = DICT_COLS[key][val];
  } else if (DICT_COLS[key] && DICT_COLS[key].default) {
    text = DICT_COLS[key].default;
  }
  return text;
}

export function getStatusMap(val, key) {
  if (DICT_COLS[key]) {
    for (const valKey in DICT_COLS[key]) {
      let { status } = DICT_COLS[key][valKey];
      if (status.includes(String(val))) {
        return DICT_COLS[key][valKey];
      }
    }
  }
  return {};
}

//生成一个函数，
// 入参如果为数字，则返回入参，
// 如果不为数字，且不为 truly , 返回 '-'
export function getNumberOrDash(val) {
  if (typeof val === 'number') {
    return val;
  }
  if (val) {
    return val;
  }
  return '--';
}

/**
 * @param {string} str - The string to be truncated.
 * @param {number} maxLength - The maximum length of the string. Default is 11.
 * @param {string} symbol - The symbol to be appended at the end of the truncated string. Default
 *   is '...'.
 * @return {string} The truncated string with the symbol appended, if necessary.
 */

export function truncateString(str, maxLength = 11, symbol = '...') {
  if (str.length <= maxLength) {
    return str;
  }
  return str.substring(0, maxLength) + symbol;
}

/**
 * Checks if a property value of an object is truthy.
 *
 * @param {object} obj - The object to check.
 * @param {string} property - The property name to check.
 * @return {boolean} True if the property value is truthy, false otherwise.
 */
export function checkPropertyTruthy(property, obj) {
  return Boolean(obj[property]);
}

/**
 * @description 重置时间
 * @param {String} str
 * @param {String} before
 * @param {String} after
 * @return {String}
 */
export function replaceStrSymbol(str = '', before = '-', after = '.') {
  if (!str || str === '') {
    return '';
  }
  if (str && str.includes(before)) {
    return str.split(before).join(after);
  }
  return str;
}

/**
 * @description 日期格式转换
 * @param dateStr
 * @returns {*}
 */
export function convertDateFormat(dateStr) {
  return dateStr.replace(/[-.]/g, '/');
}

export function isPhone(str) {
  return /^1[3456789]\d{9}$/.test(str);
}

/**
 * @description 获取url中的参数
 * @param {String} origin
 * @param {String} params
 * @return {String}
 */
export function attachQueryParams(origin, params) {
  if (typeof origin !== 'string' || typeof params !== 'string') {
    return origin;
  }
  if (origin.includes('?')) {
    return origin + '&' + params;
  }
  return origin + '?' + params;
}

/**
 * @description 校验列表值不为空
 * @params  { any } item
 */
export function isValidValue(item) {
  if (item === null) {
    return false;
  }
  if (typeof item === 'number') {
    return true;
  }
  return isString(item) && item.toString().length > 0;
}

/**
 * @description 计算两个时间点的时间差
 * @param { Number } lastTime
 * @param { Number } currentTime
 * @return {String} x时间单位前
 */

export function calcLastTime(lastTime, currentTime) {
  if (!lastTime || !currentTime) {
    return '';
  }
  const diff = currentTime - lastTime;
  const minutes = Math.floor(diff / 1000 / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) {
    return `${days}天前`;
  } else if (hours > 0) {
    return `${hours}小时前`;
  } else if (hours <= 0) {
    return `1小时内`;
  }
  return '';
}

/**
 * @description 根据文本内容和长度截取文本
 * @param { String } text
 * @param { Number } maxWidth
 */
export function wrapText(text, maxWidth) {
  const words = text.split('');
  let currentWidth = 0;
  let wrappedText = '';

  function getCharWidth(char) {
    // 假设中文字符宽度为2，其他字符宽度为1
    return /[\u4e00-\u9fa5]/.test(char) ? 2 : 1;
  }

  for (let i = 0; i < words.length; i++) {
    const charWidth = getCharWidth(words[i]); // 假设我们有一个函数来获取字符宽度
    if (currentWidth + charWidth > maxWidth) {
      wrappedText += '&#8203;';
      currentWidth = 0;
    }
    wrappedText += words[i];
    currentWidth += charWidth;
  }
  return wrappedText;
}

/**
 * @description 格式化价格
 * @param { Object } item
 * @param { String } key
 * @return { String }
 */
export function groupMoneyValue(item, key) {
  if (!isTruly(item[key])) {
    return '';
  }
  const { int, float } = filterPrice(item[key]);
  return `¥${int}${float}`;
}

/**
 * @description 格式化价格
 * @return {Object}
 * @param val
 */
export function filterPrice(val) {
  if (isNaN(val)) {
    return {};
  }
  const result = {
    int: '0',
    float: '.00',
  };
  if (val === 0) {
    return result;
  }
  if (!val) {
    return val;
  }
  let p = val.toString();
  let [int = '0', float = '00'] = p.split('.');

  if (!float) {
    float = '00';
  }

  if (float.length === 1) {
    float += '0';
  }
  float = '.' + float.substring(0, 2);

  return {
    int,
    float,
  };
}

/**
 * @description 从props中获取数据(标签展示ABCD类customerServiceTags)
 * @param { Object } chanceItem
 * @param { Object } i
 */

export function getDataByKey(chanceItem, i) {
  let value = chanceItem[i.key];
  if (['provinceName', 'sourTerminal', 'paymentMode_dictText'].includes(i.key)) {
    value = i.groupValue(chanceItem);
  }
  if (!isTruly(value)) {
    i.show = false;
    return i;
  }
  i.show = isFunction(i.showCondition) ? i.showCondition(chanceItem) : true;
  i.value = value;

  if (i.getValue) {
    i.value = i.getValue(value);
  }
  if (i.groupValue) {
    i.value = i.groupValue(chanceItem, i.key);
  }
  if (!isString(i.value) && !Array.isArray(i.value)) {
    i.show = false;
  }

  if (i.isFull) {
    return i;
  }
  if (i.value.indexOf('¥') === 0 || i.value.includes('㎡')) {
    i.isFull = i.value.length >= 14;
    return i;
  }
  // let word = i.value.match(/[a-zA-Z0-9]/gi);
  // if (Array.isArray(word)) {
  //   if (i.value.length === word.length) {
  //     i.isFull = word.length > 14;
  //     return i;
  //   }
  //   i.isFull = i.value.length - word.length + Math.ceil(word.length / 2) >= 7;
  //   return i;
  // }
  // i.isFull = i.value.length > 7;
  return i;
}
/**
 * @description 处理图片压缩
 * @param {String } url 图片链接
 * @param {Boolean} isBig 是否大图预览
 * @returns {string}
 */
export function handleCompressImg(url, isBig = false) {
  return handleOssImageUrl(url, isBig ? PREVIEW_IMG_COMPRESS_PARAMS : CARD_IMG_COMPRESS_PARAMS);
}

/**
 * 判断OSS图片链接是否包含处理参数(x-oss-process)，如果没有则添加指定的参数。
 * 同时考虑链接中可能已存在的其他查询参数。
 *
 * @param {string} url - OSS图片的原始链接。
 * @param {string} OSSParams - 用户提供的OSS处理参数，例如 'image/resize,m_lfit,w_100,h_100'。
 * @returns {string} - 处理后的链接，确保包含指定的x-oss-process参数及原有的查询参数。
 */
export function handleOssImageUrl(url, OSSParams) {
  if (!url || !OSSParams) {
    return url;
  }
  const urlObj = new URL(url);
  const searchParams = new URLSearchParams(urlObj.search);

  // 检查是否已包含x-oss-process参数
  if (searchParams.has('x-oss-process')) {
    // 如果已包含，则直接返回原链接
    return url;
  }
  // 如果不包含，则添加x-oss-process参数
  searchParams.set('x-oss-process', OSSParams);
  urlObj.search = searchParams.toString();
  return urlObj.href;
}

/**
 * @description 特殊金额检查要求
 * @param {Object} rule
 * @param {String} value
 * @return {Promise}
 */
export function checkNumberInput(rule, value) {
  if (!value) {
    return Promise.resolve();
  }
  value = value + '';
  if (!value.includes('.') && value.length > 9) {
    return Promise.reject('整数位最多输入9位数字');
  }

  let numberValue = Number(value);

  if (isNaN(numberValue)) {
    return Promise.reject('请输入数字');
  }
  if (numberValue <= 0) {
    return Promise.reject('请输入大于0的数字');
  }

  let int = value.split('.')[0];
  if (int.toString().length > 9) {
    return Promise.reject('请输入9位及以内的数字');
  }

  return isIntOrTwoDecimal(numberValue) ? Promise.resolve(true) : Promise.reject('请输入整数或最大两位小数');
}

/**
 * @description 格式化数字
 * @param value
 * @param showLastZero 是否显示最后一位小数
 */
export function formatterNumber(value, showLastZero = true) {
  if (isNaN(value)) {
    return value;
  }
  if (showLastZero) {
    let fixNumber = Number(value).toFixed(2);
    let deleteSubZero = parseFloat(fixNumber.toString());
    return value ? deleteSubZero.toFixed(2) : '';
  }
  return Number(value).toString();
}

/**
 * @description 获取URL参数并转换为对象
 * @returns {{}}
 */
export function getUrlParamsAsObject() {
  // 获取查询字符串部分（即URL中?之后的部分）
  const queryString = window.location.search;
  // 创建 URLSearchParams 对象
  const urlParams = new URLSearchParams(queryString);

  // 初始化一个空对象用于存储参数键值对
  let paramsObj = {};

  // 遍历所有的键值对并添加到对象中
  for (let [key, value] of urlParams.entries()) {
    try {
      // 尝试解码和解析JSON字符串
      paramsObj[key] = JSON.parse(decodeURIComponent(value));
    } catch (e) {
      console.log('解析JSON字符串失败：', e);
      // 如果不是有效的JSON字符串，则直接赋值
      paramsObj[key] = decodeURIComponent(value);
    }
  }

  return paramsObj;
}

/**
 * 获取url参数
 * @param originUrl
 * @param type 1 2 1获取search参数  2获取hash参数
 * @returns {{}}
 */
export function getUrlParams(type, originUrl) {
  let url = originUrl;
  if (type === 1) {
    url = url || window.location.href;
    url = url.split('#')[0];
  }
  if (type === 2) {
    url = window.location.hash;
  }
  let result = {};
  let params = new URLSearchParams(url.split('?')[1]);
  for (const [key, value] of params.entries()) {
    result[key] = value;
  }
  return result;
}

/**
 * 获取所有url参数
 * @returns {{}}
 */
export function getUrlAllParams(url) {
  return Object.assign(getUrlParams(1, url), getUrlParams(2, url));
}

/**
 * @description 格式化日期
 * @param {Date|string} date - 日期对象或日期字符串
 * @param {string} format - 格式化字符串，例如 "YYYY-MM-DD HH:mm:ss"
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  let newDate = date;
  if (!newDate) {
    return date;
  }
  if (isString(newDate)) {
    newDate = new Date(convertDateFormat(newDate));
  }
  if (isInvalidDate(newDate)) {
    return date;
  }

  const map = {
    YYYY: newDate.getFullYear(),
    MM: ('0' + (newDate.getMonth() + 1)).slice(-2),
    DD: ('0' + newDate.getDate()).slice(-2),
    HH: ('0' + newDate.getHours()).slice(-2),
    hh: ('0' + newDate.getHours()).slice(-2),
    mm: ('0' + newDate.getMinutes()).slice(-2),
    ss: ('0' + newDate.getSeconds()).slice(-2),
  };
  dayjs();
  return format.replace(/YYYY|MM|DD|HH|hh|mm|ss/g, (match) => map[match]);
}

export function getDayJsFormat(date, format = 'YYYY-MM-DD HH:mm:ss') {
  return dayjs(formatDate(date, format));
}

/**
 * 生成日期范围
 * @param N
 * @param formatter
 * @returns {{startTime: Date, endTime: Date}}
 */
export function generateDateRange(N, formatter = 'YYYY-MM-DD hh:mm:ss') {
  const start = new Date();
  const end = new Date();
  start.setHours(0, 0, 0);
  end.setHours(23, 59, 59);
  if (N > 1) {
    start.setDate(start.getDate() - (N - 1));
  }
  return {
    startTime: formatDate(start, formatter),
    endTime: formatDate(end, formatter),
    startDate: start,
    endDate: end,
  };
}

/**
 * @description 千分位转换数字
 * @param {String | Number} inNum
 * @return {String} outNum
 */
export function kiloSplit(inNum) {
  if (!isString(inNum) && !isNumber(inNum)) {
    return inNum || '-';
  }
  let str = '' + inNum;
  return str.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// 播放器打开全屏
export function mediaLaunchFullscreen(element) {
  if (element.requestFullscreen) {
    element.requestFullscreen();
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen();
  } else if (element.msRequestFullscreen) {
    element.msRequestFullscreen();
  } else if (element.oRequestFullscreen) {
    element.oRequestFullscreen();
  } else if (element.webkitRequestFullscreen) {
    element.webkitRequestFullScreen();
  }
}

// 播放器关闭全屏
export function mediaExitFullscreen() {
  if (document.fullscreenElement !== null) {
    if (document.exitFullscreen) {
      document.exitFullscreen().then();
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen();
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen();
    } else if (document.oRequestFullscreen) {
      document.oCancelFullScreen();
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen();
    }
  }
}
export function returnImg(url) {
  if (!url) {
    return null;
  }
  let arr = url.split('/');
  let reg = /[\u4e00-\u9fa5]/g;
  for (let i = 0; i < arr.length; i++) {
    if (arr[i].match(reg)) {
      let str = encodeURIComponent(arr[i]);
      arr[i] = str;
    }
  }
  return arr.join('/');
}

// ... existing code ...

/**
 * @description 对象数组去重（根据指定key）
 * @param {Array} arr - 需要去重的对象数组
 * @param {String} key - 用于判断是否重复的字段名
 * @return {Array} 去重后的新数组
 */
export function uniqueArrayByKey(arr, key) {
  if (!Array.isArray(arr) || !key) {
    console.warn('参数错误：请传入有效的数组和key');
    return arr;
  }

  const map = new Map();
  return arr.filter((item) => {
    const keyValue = item[key];
    if (map.has(keyValue)) {
      return false; // 已存在，过滤掉
    } else {
      map.set(keyValue, true); // 记录该值已出现
      return true;
    }
  });
}

// ... existing code ...
