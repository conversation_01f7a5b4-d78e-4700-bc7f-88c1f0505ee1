/**
 * @description 这里维护主应用提供的方法和与主应用交互的内容
 */
import { isQianKun } from '@/utils/isEnv';
import { isObject } from '@/utils/isType';

export const mainPlatformApi = {
  // 初始化环境检测
  _init() {
    // 判断SYNAPI是否为空
    if (!isQianKun() && !window?.$SYN_CRM) {
      console.error('[错误]：主应用SYNAPI未检测到，请检查！');
      return false;
    }
    return true;
  },

  /**
   * 打开新标签页
   * @param data
   */
  openTab(data) {
    window.open(data.url);
  },

  /**
   * 关闭主应用中当前tab
   * @returns {string}
   */
  closeCurrentTab() {
    let status = this._init();
    if (status === false) {
      return;
    }
    window?.$SYN_CRM?.portalCloseCurTab();
  },

  /**
   * 获取主应用token
   * @returns {string}
   */
  getToken() {
    let status = this._init();
    if (status === false) {
      return;
    }
    return window?.$SYN_CRM?.portalGetToken();
  },

  /**
   * 跳转页面
   * 注意：name和path有一即可
   * @param name 路由名称，唯一性
   * @param path 路由路径，存在问题，暂时不支持
   * @param query 路由参数
   */
  goToPage({ name, path, query }) {
    let status = this._init();
    if (status === false) {
      return;
    }
    window?.$SYN_CRM?.portalGoToPage({ name, path, query });
  },

  /**
   * 跳转登录页
   * @param query 登录页参数
   */
  goToLogin(query) {
    let status = this._init();
    if (status === false) {
      return;
    }
    if (isObject(query)) {
      this.goToPage({ path: '/login', query });
    } else {
      this.goToPage({ path: '/login' });
    }
  },
  /**
   * 调用主应用重新登录
   */
  portalReLogin() {
    let status = this._init();
    if (status === false) {
      return;
    }
    window?.$SYN_CRM?.portalReLogin();
  },
  /**
   * 调用主应用退出登录
   */
  reLogin() {
    let status = this._init();
    if (status === false) {
      return;
    }
    window?.$SYN_CRM?.portalReLogin();
  },

  // 刷新token
  refreshToken() {
    let status = this._init();
    if (status === false) {
      return;
    }
    window?.$SYN_CRM?.portalRefreshToken();
  },

  // 获取主平台用户信息
  getUserInfo() {
    let status = this._init();
    if (status === false) {
      return;
    }
    return window?.$SYN_CRM?.getUserInfo();
  },

  // 获取主平台当前门店信息
  getUserMdInfo() {
    let status = this._init();
    if (status === false) {
      return;
    }
    return window?.$SYN_CRM?.getUserMdInfo();
  },

  /**
   * 主应用切换全屏
   * @param isFullscreen 是否全屏，默认true
   */
  portalToggleFullscreen(isFullscreen = true) {
    let status = this._init();
    if (status === false) {
      return;
    }
    window?.$SYN_CRM?.portalToggleFullscreen(isFullscreen);
  },
  portalGetMicroAppRoutes(routes) {
    window?.$SYN_CRM?.portalGetMicroAppRoutes(routes);
  },
};

export default mainPlatformApi;
