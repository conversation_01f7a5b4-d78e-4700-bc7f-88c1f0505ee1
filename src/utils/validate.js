/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

export function isPhone(phone) {
  return /^1[3456789]\d{9}$/.test(phone);
}

export function isIntOrTwoDecimal(value) {
  const regex = /^[1-9]\d{0,8}(\.\d{0,2})?$/;
  return regex.test(value);
}

/**
 * URL地址
 * @param {*} s
 */
export function isURL(s) {
  return /^http[s]?:\/\/.*/.test(s);
}
