/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

export function isPhone(phone) {
  return /^1[3456789]\d{9}$/.test(phone);
}

export function isIntOrTwoDecimal(value) {
  const regex = /^[0-9]\d*(\.\d{0,2})?$/;
  return regex.test(value);
}

export function isPositiveInteger(value) {
  const regex = /^[1-9]\d*$/;
  return regex.test(value);
}

export function isWechatId(value) {
  return /^[-_a-zA-Z]([-_a-zA-Z0-9]{5,19})+$/.test(value);
}
