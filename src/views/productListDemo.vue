<script setup>
import { ref, onMounted } from 'vue';
import { ProductListPage } from '@/views/designTool/components/importBaseMap/productList';

const products = ref([]);
const loading = ref(false);
const selectedProductId = ref(null);

// Mock product data
const mockProducts = [
  {
    id: 1,
    title: '左岸风度',
    description: '132m² | 三室两厅',
    image: 'https://via.placeholder.com/300x200/f0f0f0/333333?text=Product+1'
  },
  {
    id: 2,
    title: '阳光家园小区名称超过显示在',
    description: '132m² | 三室两厅',
    image: 'https://via.placeholder.com/300x200/e0e0e0/333333?text=Product+2'
  },
  {
    id: 3,
    title: '阳光家园',
    description: '99.21m² | 三室两厅',
    image: 'https://via.placeholder.com/300x200/d0d0d0/333333?text=Product+3'
  },
  {
    id: 4,
    title: '左岸风度',
    description: '132m² | 三室两厅',
    image: 'https://via.placeholder.com/300x200/c0c0c0/333333?text=Product+4'
  },
  {
    id: 5,
    title: '左岸风度',
    description: '132m² | 三室两厅',
    image: 'https://via.placeholder.com/300x200/b0b0b0/333333?text=Product+5'
  },
  {
    id: 6,
    title: '左岸风度',
    description: '132m² | 三室两厅',
    image: 'https://via.placeholder.com/300x200/a0a0a0/333333?text=Product+6'
  },
  {
    id: 7,
    title: '左岸风度',
    description: '132m² | 三室两厅',
    image: 'https://via.placeholder.com/300x200/909090/333333?text=Product+7'
  },
  {
    id: 8,
    title: '左岸风度',
    description: '132m² | 三室两厅',
    image: 'https://via.placeholder.com/300x200/808080/333333?text=Product+8'
  }
];

const loadProducts = () => {
  loading.value = true;
  // Simulate API call
  setTimeout(() => {
    products.value = mockProducts;
    loading.value = false;
  }, 1500);
};

const handleSearch = (query) => {
  console.log('Search query:', query);
  // Implement search logic here
};

const handleLocationChange = (location) => {
  console.log('Location changed:', location);
  // Implement location filter logic here
};

const handleProductClick = (product) => {
  console.log('Product clicked:', product);
};

const handleProductSelect = (product) => {
  selectedProductId.value = selectedProductId.value === product.id ? null : product.id;
  console.log('Product selected:', product, 'Selected ID:', selectedProductId.value);
};

const showEmpty = () => {
  products.value = [];
  loading.value = false;
};

const showLoading = () => {
  loading.value = true;
  products.value = [];
};

onMounted(() => {
  loadProducts();
});
</script>

<template>
  <div class="demo-page">
    <div class="demo-header">
      <h1>Product List Demo</h1>
      <div class="demo-controls">
        <a-button @click="loadProducts">Load Products</a-button>
        <a-button @click="showLoading">Show Loading</a-button>
        <a-button @click="showEmpty">Show Empty</a-button>
      </div>
    </div>
    
    <div class="demo-content">
      <product-list-page
        :products="products"
        :loading="loading"
        :container-height="452"
        :searchable="true"
        :selectable="true"
        :selected-product-id="selectedProductId"
        @search="handleSearch"
        @location-change="handleLocationChange"
        @product-click="handleProductClick"
        @product-select="handleProductSelect"
      />
    </div>
  </div>
</template>

<style scoped lang="stylus">
.demo-page
  height 100vh
  display flex
  flex-direction column
  padding 20px
  
.demo-header
  margin-bottom 20px
  
  h1
    margin 0 0 16px 0
    @extends .title-large
    
  .demo-controls
    display flex
    gap 12px
    
.demo-content
  flex 1
  min-height 0
  border 1px solid #d9d9d9
  border-radius 8px
  padding 20px
  background-color #fff
</style>
