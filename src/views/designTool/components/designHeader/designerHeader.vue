<script setup>
  import { ref } from 'vue';
  import { HEADER_BUTTONS } from './headerConfig';
  import UserInfoMenu from '@/views/designTool/components/designHeader/userInfoMenu.vue';
  import Icon from '@/components/icon/icon.vue';
  import logo from '@/assets/images/logo.png';
  import { debounce } from 'lodash';
  import { useCaseStatusStore } from '@/store/modules/caseStatusStore';
  import { message } from 'ant-design-vue';
  import { createModal } from '@/hooks/useModal';
  import { useUserStore } from '@/store/modules/userStore';
  import { useDesignCase } from '@/hooks/useDesignCase';

  const { getPlanStatusFromEngine, fetchDeviceSystemIds } = useDesignCase();
  const userStore = useUserStore();
  const caseStatusStore = useCaseStatusStore();
  const buttons = ref(HEADER_BUTTONS());
  const emit = defineEmits(['onAction']);
  const active = ref('');
  const props = defineProps({
    engine: {
      type: Object,
      default: () => {},
    },
  });

  const mustSave = [
    { action: 'generateWorkDrawing', tip: '方案未保存，无法生成施工图，您是否确认保存?' },
    { action: 'quote', tip: '方案未保存，无法生成清单报价，您是否确认保存?' },
    { action: 'generateProposal', tip: '方案未保存，无法生成提案，您是否确认保存?' },
  ];
  caseStatusStore.$subscribe((_, state) => {
    let statusMap = state.statusMap;
    buttons.value = HEADER_BUTTONS().map((item) => {
      if (item.status) {
        item.disabled = !statusMap[item.status];
      }
      return item;
    });
  });

  const handleCaseControl = async (item) => {
    if (item.disabled) {
      return;
    }
    // 用户未保存方案
    const isSave = 'code' in userStore.saveCaseData;
    // 是否必须保存
    const isMustSave = mustSave.find((i) => i.action === item.action);
    // 已绘制>1个封闭房间和在封闭房间内放置>1个商品
    let deviceSystemIds = [];
    // getPlanStatus 返回值说明：
    // drawContent: 是否有墙体绘制内容
    // hasBackgroundImage: 是否有底图
    // hasIntelligentGoods: 是否选择了智能商品
    const { drawContent, hasBackgroundImage, hasIntelligentGoods } = await getPlanStatusFromEngine(props.engine);
    // 用户未进行任何操作
    const isUserNoOperation = !drawContent && !hasBackgroundImage && !hasIntelligentGoods;

    console.log('isSave', isSave);
    // 清空/清单报价/生成施工图/生成提案同理,点击出现提示
    if ((isMustSave || item.action === 'clear') && isUserNoOperation) {
      message.warning('请先设计方案!');
      return;
    }

    // 用户已进行操作
    if (!isUserNoOperation && (item.action === 'save' || isMustSave)) {
      deviceSystemIds = await fetchDeviceSystemIds(props.engine);
      console.log('getDeviceSystemIds', deviceSystemIds);
    }

    // 必须保存后才能操作的功能（生成施工图、生成清单报价、生成提案 ）
    if (isMustSave) {
      // 未保存方案
      if (!isSave) {
        createModal('confirm', {
          hideCancel: false,
          maskClosable: true,
          closable: true,
          content: isMustSave.tip,
          onOk: () => {
            console.log('onOk');
            emit('onAction', {
              action: 'save',
            });
          },
          onCancel: () => {
            console.log('onCancel');
          },
        });
        return;
      } else {
        // 保存过方案，用户再次操作后，但未绘制至少1个封闭房间和在封闭房间内放置至少1个商品时
        if (deviceSystemIds.length === 0) {
          message.warning('您未在闭合房间内放置商品!');
          return;
        }
      }
    }
    console.log('props.engine', props.engine);
    const executeAction = () => {
      active.value = item.action;
      emit('onAction', item);
    };

    if (item.debounce) {
      debounce(executeAction, item.debounce)();
    } else {
      executeAction();
    }
  };
</script>

<template>
  <div class="designer-header">
    <div class="header-left">
      <div class="logo">
        <img :src="logo" alt="" />
      </div>
    </div>
    <div class="header-center">
      <div
        :class="[
          'header-item',
          { active: active === item.action, disabled: item.disabled, 'padding-content': !item.gap },
        ]"
        v-for="(item, index) in buttons"
        type="default"
        :key="index"
        @click="handleCaseControl(item)">
        <template v-if="item.gap">
          <div class="gap"></div>
        </template>
        <template v-else>
          <div class="icon">
            <Icon :icon="item.icon" color="#000" :size="20"></Icon>
          </div>
          <div class="label">
            {{ item.label }}
          </div>
        </template>
      </div>
    </div>
    <div class="header-right">
      <user-info-menu></user-info-menu>
    </div>
  </div>
</template>

<style scoped lang="stylus">
  .designer-header
    display flex
    position absolute
    top 0
    left 60px
    z-index 11;
    align-items center
    width calc(100% - 60px)
    justify-content space-between
    padding 4px 20px 4px 0
    height 50px
    background: linear-gradient(90deg, #E8EDF0 11.97%, rgba(232, 238, 240, 0.00) 16.79%, rgba(232, 238, 240, 0.00) 86%, #E9EEF1 100%);
  .header-left
    display flex
    align-items center
    gap 20px
    .logo
      position absolute
      top 0
      left 0
      padding-left 12px
      width 170px
      height 50px
      display flex
      align-items center
      justify-content center
      color #fff
      font-size 20px
    .title
      color #fff
      @extends .title-normal
  .header-center
    position absolute
    top 0
    left 50%
    transform translateX(-50%)
    display flex
    width 468px
    padding 0 20px
    height 50px
    box-shadow 0 4px 8px 0 rgba(0, 0, 0, 0.10)
    border-radius 0 0 10px 10px
    justify-content center
    gap 20px
    background #fff
  .header-item
    display flex
    flex-shrink 0
    flex-direction column
    justify-content center
    align-items center
    color rgba(255, 255, 255, 0.8)
    cursor pointer
    position relative
    &:hover
      background var(--card-hover-gb-color)
      border-radius 4px
    &.padding-content
      padding 6px 4px
    &.active
      color var(--opn-color-primary)
    .label
      margin-top 2px
      @extends .content-small
      color: #000
      font-size 11px
      opacity 0.65
    &.disabled
      pointer-events none
      opacity 0.4;
    .gap
      height 24px
      width 1px
      background #D9D9D9
  .header-right
    display flex
    flex-direction row
    align-items center
    gap 10px
</style>
