<script setup>
  import { onMounted, ref } from 'vue';
  import { _API_getServiceFeeRate, _API_getProductDiscountRate } from '@/api/userInfo.js';
  import { message } from 'ant-design-vue';
  import { useUserStore } from '@/store/modules/userStore';
  import FeeSettingModal from '@/views/designTool/components/feeSetting/FeeSettingModal.vue';

  const avatar = ref(null);
  const userStore = useUserStore();
  const visible = ref(false);
  const type = ref('serviceFee');
  const serviceFeeRate = ref(0);
  const discountRate = ref(0);
  const currentRate = ref(0);
  const tip = ref('');

  const handleDropdownOpenChange = (open) => {
    const isSave = 'code' in userStore.saveCaseData;
    console.log('dropdownOpen', open, isSave, userStore.saveCaseData);
    if (open && isSave && discountRate.value === 0) {
      tip.value = '';
      fetchRate('discount');
    }
  };

  const fetchRate = async (rateType) => {
    try {
      const params = rateType === 'serviceFee' ? {} : { code: userStore.saveCaseData?.code };
      const res = await (rateType === 'serviceFee'
        ? _API_getServiceFeeRate(params)
        : _API_getProductDiscountRate(params));
      currentRate.value = res?.data || 0;
      if (rateType === 'serviceFee') {
        serviceFeeRate.value = currentRate.value;
      } else {
        discountRate.value = currentRate.value;
      }
    } catch (error) {
      message.error('获取利率失败，请稍后重试');
      console.error('Failed to fetch rate:', error);
    }
  };

  const handleMenuClick = ({ key }) => {
    if (key === '2') {
      type.value = 'serviceFee';
      fetchRate('serviceFee');
    } else if (key === '3') {
      type.value = 'discount';
      fetchRate('discount');
    }
    visible.value = true;
  };

  // 取消按钮处理方法
  const handleCancel = () => {
    visible.value = false;
  };

  const handleSuccess = () => {
    visible.value = false;
    fetchRate(type.value);
  };
  /**
   * 组件挂载时初始化数据
   */
  onMounted(async () => {
    try {
      // 获取用户头像
      avatar.value = await userStore.getAvatar();
    } catch (error) {
      console.error('Failed to load avatar:', error);
      // 可以添加默认头像 fallback
      // avatar.value = DEFAULT_AVATAR;
    }

    const hasSavedCase = userStore.saveCaseData && 'code' in userStore.saveCaseData;

    // 设置折扣提示
    if (!hasSavedCase) {
      tip.value = '仅限当前方案的商品折扣，请先保存方案';
    }

    // 并行获取费率信息
    const fetchPromises = [fetchRate('serviceFee')];
    if (hasSavedCase) {
      fetchPromises.push(fetchRate('discount'));
    }

    await Promise.all(fetchPromises);
  });
</script>

<template>
  <div class="user-info-menu">
    <a-dropdown :trigger="['click', 'hover']" @openChange="handleDropdownOpenChange">
      <a-avatar :src="avatar" class="user-avatar ant-dropdown-open" size="large"></a-avatar>
      <template #overlay>
        <a-menu class="user-menu-dropdown" @click="handleMenuClick">
          <!-- 店铺信息头部 -->
          <div class="menu-header">
            <a-avatar :src="avatar" class="header-avatar" size="large"></a-avatar>
            <div class="header-info">
              <div class="store-name">{{ userStore?.currentMd?.mdName }}</div>
              <div class="user-phone">
                {{
                  userStore?.userInfo?.mobilePhone
                    ? userStore?.userInfo?.mobilePhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
                    : ''
                }}
              </div>
            </div>
          </div>
          <!-- 菜单分割线 -->
          <!-- <a-menu-divider /> -->
          <!-- 服务费折扣设置 -->
          <a-menu-item key="2" class="setting-item">
            <div class="setting-content">
              <div class="setting-label">
                <div class="main-text">服务费折扣设置</div>
                <div class="rate-text">{{ serviceFeeRate }}%</div>
              </div>
              <synArrowRight />
            </div>
          </a-menu-item>
          <!-- 商品折扣设置 -->
          <a-menu-item key="3" class="setting-item" :disabled="!!tip">
            <div class="setting-content">
              <div class="setting-label">
                <div class="main-text">
                  商品折扣设置
                  <span>{{ tip }}</span>
                </div>
                <div class="rate-text">{{ discountRate || 100 }}%</div>
              </div>
              <synArrowRight :style="{ opacity: tip ? 0 : 1 }" />
            </div>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <FeeSettingModal v-if="visible" :type="type" :rate="currentRate" @cancel="handleCancel" @success="handleSuccess" />
  </div>
</template>

<style scoped lang="stylus">
  .ant-dropdown-menu
    padding 0
  .user-menu-dropdown
    border-radius 10px
    box-shadow 0 4px 16px rgba(0, 0, 0, 0.1)
  .user-menu-dropdown
    :deep(.ant-dropdown-menu-item-divider)
      margin: 0
    :deep(.ant-dropdown-menu-item)
      padding 0
  .menu-header
    padding 16px 24px
    width 337px
    box-sizing: content-box
    display flex
    align-items center
    gap 20px

  .header-avatar
    flex-shrink: 0

  .header-info
    flex 1

  .store-name
    white-space nowrap
    overflow hidden
    text-overflow ellipsis
    width  277px
    font-size 16px
    font-weight 500
    color #1D2129
    margin-bottom 4px

  .user-phone
    font-size 14px
    color #86909C

  .setting-item
    padding 0 16px

  .setting-content
    display flex
    align-items center
    justify-content space-between
    width 100%
    padding 12px 24px
    height 46px
    border-top 1px solid #f0f0f0
  .setting-label
    width 100%
    display flex
    justify-content space-between
    align-items center
    gap 8px

  .main-text
    font-size 14px
    color rgba(0, 0, 0, 0.85)
    font-family "PingFang SC"
    font-size 14px
    font-weight 400
    span
      color rgba(0, 0, 0, 0.25)

  .rate-text
    font-size 14px
    color #86909C

  .arrow-icon
    color #C9CDD4
    font-size 16px
</style>
