<template>
  <div class="left-sidebar-panel" :style="{ width: sidebarWidth + 'px' }">
    <div class="sidebar-tabs-vertical">
      <div
        v-for="(tab, idx) in props.tabs"
        :key="tab.key"
        :class="['sidebar-tab-vertical', { active: idx === activeTabIdx }]"
        @click="setActiveTab(idx)">
        <div class="icon-tab" :style="styleObject(idx, tab)"></div>
        <div class="icon-label">{{ tab.label }}</div>
      </div>
    </div>
    <div class="sidebar-secondary-content">
      <slot :name="tabs[activeTabIdx].key" />
    </div>
  </div>
</template>

<script setup>
  import { computed, ref } from 'vue';
  const props = defineProps({
    tabs: { type: Array, default: () => [] },
  });
  const emit = defineEmits(['changeActive']);
  const sidebarWidth = ref(300);
  const activeTabIdx = ref(0);
  function setActiveTab(idx) {
    activeTabIdx.value = idx;
    emit('changeActive', idx);
  }

  const styleObject = computed(() => {
    return (idx, tab) => {
      return idx === activeTabIdx.value
        ? {
            background: `url(${tab.activeIcon}) 0 0 no-repeat`,
            backgroundSize: 'contain',
            '--hover-bg': `url(${tab.hoverIcon})`,
          }
        : {
            background: `url(${tab.icon}) 0 0 no-repeat`,
            backgroundSize: 'contain',
            '--hover-bg': `url(${tab.hoverIcon})`,
          };
    };
  });
</script>

<style scoped lang="stylus">
  .left-sidebar-panel {
    display: block;
    height: 100%;
    background: #fafbfc;
    user-select: none;
    z-index: 20;
  }
  .sidebar-tabs-vertical {
    padding-top: 40px;
    display: flex;
    flex-direction: column;
    width: 60px;
    background: url('@/assets/images/sidebar-bg.png');
    background-size: cover;
    height: 100%;
    z-index: 20;
    position fixed
    top 0
  }
  .icon-tab
    width 36px
    height 36px

  .sidebar-tab-vertical {
    padding: 12px 0;
    text-align: center;
    @extends .comment-small;
    color var(--d9-color)
    font-size:11px;
    cursor pointer;
    display flex
    flex-direction column
    align-items center
    &:hover {
      color: #e6d3a8;
      .icon-tab {
        background var(--hover-bg) 0 0 no-repeat !important
        background-size contain !important
      }
    }

  }
  .icon-label
    line-height 20px
    margin-top 4px
  .sidebar-tab-vertical.active {
    color: #e6d3a8;
    font-weight: 500;
  }
  .sidebar-secondary-content {
    position: absolute;
    top: 0;
    left: 60px;
    right: 0;
    bottom: 8px;
    overflow: hidden; /* 改为 hidden，让子组件处理滚动 */
    background: #fff;
    z-index: 10;
    border-radius 10px
    box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0px 9px 29px 0px rgba(0, 0, 0, 0.05), 0px 12px 48px 16px rgba(0, 0, 0, 0.03);

    /* 为子组件提供完整的flex布局容器 */
    display: flex;
    flex-direction: column;
  }


  .sidebar-dragger {
    position: absolute;
    top: 0;
    right: 0;
    width: 6px;
    height: 100%;
    cursor: ew-resize;
    background: transparent;
    z-index: 10;
  }
  .sidebar-dragger:hover {
    background: #e6f7ff;
  }
</style>
