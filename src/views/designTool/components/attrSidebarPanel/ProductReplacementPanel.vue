<template>
  <a-modal v-model:open="open" :title="title" :maskClosable="false" @cancel="handleCancel" @ok="handleConfirm">
    <div class="product-replacement-panel">
      <div class="filters">
        <a-input-search v-model:value="searchQuery" @search="onSearch" placeholder="搜索"></a-input-search>
        <a-button v-if="isSearch" @click="handleClearSearch">x</a-button>
        <!-- 主分类网格 -->
        <div class="main-category-grid" v-if="!isSearch">
          <div
            v-for="category in mainCategories"
            :key="category.key"
            :class="['main-category-item', { active: category.key === activeMainCategoryIdx }]"
            @click="setActiveMainCategory(category.key)">
            {{ category.label }}
          </div>
        </div>

        <!-- 2. 横向二级类目 label -->
        <div class="secondary-category-labels" ref="categoryLabelRef" v-if="!isSearch">
          <span :class="['category-label', { active: activeCategoryIdx === 'all' }]" @click="setActiveCategory('all')">
            全部
          </span>
          <span
            v-for="cat in filteredCategories"
            :key="cat.key"
            :class="['category-label', { active: cat.key === activeCategoryIdx }]"
            @click="setActiveCategory(cat.key)">
            {{ cat.label }}
          </span>
        </div>
      </div>
      <div class="product-grid">
        <div
          v-for="product in filteredProducts"
          :key="product.id"
          class="product-card"
          :class="{ selected: product.componentCode === selectedProductId }"
          @click="selectProduct(product)">
          <img :src="product.mainImage" :alt="product.componentDesc" class="product-image-placeholder" />
          <div class="product-name">{{ product?.componentDesc }}</div>
          <div class="product-price" v-if="product?.pcPrice">¥ {{ product?.pcPrice?.toFixed(2) }}</div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, computed } from 'vue';
  import { _API_getIntelligentData } from '@/api/intelligentApi';
  import { _API_searchDeviceTree } from '@/api/deviceApi';

  const props = defineProps({
    title: {
      type: String,
      default: '商品替换',
    },
    defaultMainCategoryId: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['cancel', 'confirm']);

  const open = ref(true);

  // --- 状态 ---
  const searchQuery = ref('');
  const mainCategories = ref([]); // 一级标签列表
  const activeMainCategoryIdx = ref(null); // 一级标签选中的key
  const activeCategoryIdx = ref(null); // 二级标签选中的key
  const isSearch = ref(false);
  const searchList = ref([]);
  const selectedProduct = ref(null);

  const selectedProductId = computed(() => selectedProduct.value?.componentCode || '');

  // --- 计算属性 ---
  const filteredProducts = computed(() => {
    if (isSearch.value) {
      return searchList.value;
    }

    if (mainCategoryObj.value && activeCategoryIdx.value === 'all') {
      return mainCategoryObj.value?.subModules?.flatMap((m) => m.list) || [];
    }

    if (mainCategoryObj.value) {
      return mainCategoryObj.value?.subModules?.filter((item) => item.key === activeCategoryIdx.value)?.[0]?.list || [];
    }

    return [];
  });

  // 二级tab
  const filteredCategories = computed(() => {
    if (mainCategoryObj.value) {
      return mainCategoryObj.value.subModules?.map((subItem) => ({
        key: subItem.key,
        label: subItem.label,
      }));
    }
    return [];
  });

  // 一级标签选中的标签对象
  const mainCategoryObj = computed(() => {
    const list = mainCategories.value?.filter((item) => item.key === activeMainCategoryIdx.value);
    if (list?.length > 0) {
      return list[0];
    }
    return null;
  });

  // --- 方法 ---
  const selectProduct = (product) => {
    selectedProduct.value = product;
  };

  const handleCancel = () => {
    emit('cancel');
  };

  const handleConfirm = () => {
    if (selectedProduct.value) {
      emit('confirm', selectedProduct.value);
    } else {
      // 如果没有选择任何产品，也可以仅关闭弹窗
      emit('cancel');
    }
  };

  const handleClearSearch = (e) => {
    e.stopPropagation();
    initPage();
    isSearch.value = false;
  };
  const onSearch = () => {
    console.log('onSearch');
    if (!searchQuery.value) {
      return;
    }

    isSearch.value = true;
    getSearchList();
  };

  const getSearchList = () => {
    if (!searchQuery.value) {
      return;
    }

    _API_searchDeviceTree({ query: searchQuery.value }).then((res) => {
      const { code, data } = res;
      if (code === '0') {
        searchList.value = data || [];
      }
    });
  };
  // 设置一级标签
  const setActiveMainCategory = (key) => {
    activeMainCategoryIdx.value = key;
    activeCategoryIdx.value = 'all';
  };

  // 设置二级标签
  const setActiveCategory = (key) => {
    activeCategoryIdx.value = key;
  };

  const initPage = () => {
    activeMainCategoryIdx.value = props.activeMainCategoryIdx;
    activeCategoryIdx.value = 'all';
  };

  const getCategoryData = () => {
    _API_getIntelligentData().then((res) => {
      const { code, data } = res;
      if (code === '0') {
        // 处理数据转换
        const $data = data.map((item) => ({
          key: item.systemId,
          label: item.systemName,
          subModules: item.subModules.map((subItem) => ({
            key: subItem.baseSubModuleId,
            label: subItem.subModuleName,
            list: subItem.components,
          })),
        }));
        mainCategories.value = $data;
        if ($data.length > 0) {
          const category = $data[0];

          activeMainCategoryIdx.value = category.key;
          activeCategoryIdx.value = 'all';
          // 默认第一条选中
          //  allCategories.value.furniture = category.subModules;
        }
      }
    });
  };

  getCategoryData();
  initPage();
</script>

<style scoped lang="stylus">
  .product-replacement-panel
    width 340px
    display flex
    flex-direction column
    background-color #fff
    padding 12px

  .panel-header
    padding-bottom 12px
    .panel-title
      font-size 16px
      font-weight 600
      text-align center
      color #333

  .filters
    display flex
    flex-direction column
    gap 12px
    margin-bottom 16px

  .main-category-grid,
  .secondary-category-labels
    display flex
    align-items center
    justify-content start
    gap 12px

  .main-category-item
    color var(--text-color)
    font-size 14px
    font-weight 500
    &.active
      color red

  .category-label
    color var(--text-color)
    font-size 12px
    font-weight 500
    &.active
      color pink

  .product-grid
    flex-grow 1
    display grid
    grid-template-columns repeat(2, 1fr)
    gap 12px
    overflow-y auto
    max-height 300px
    padding 4px

  .product-card
    border 1px solid #e8e8e8
    border-radius 4px
    padding 8px
    text-align center
    cursor pointer
    overflow hidden
    transition all 0.2s
    &:hover
      border-color #1890ff
      box-shadow 0 2px 8px rgba(0,0,0,0.09)
    &.selected
      border-color #1890ff
      border-width 2px
      padding 7px
    .product-image-placeholder
      width 100%
      height 100px
      object-fit contain
      background-color #f0f2f5
      margin-bottom 8px
      border-radius 3px
    .product-name
      font-size 12px
      color #333
      white-space nowrap
      overflow hidden
      text-overflow ellipsis
    .product-dims
      font-size 11px
      color #888
    .product-price
      font-size 12px
      color #f5222d
      font-weight 500

  .panel-footer
    display flex
    justify-content flex-end
    gap 8px
    margin-top 16px
    padding-top 16px
    border-top 1px solid #f0f0f0
</style>
