<template>
  <!-- 空间属性 -->
  <SpaceAttr v-if="eventType === 'Space'" :attrs="spaceAttributes" @on-callback="actionCallback" />
  <!-- 商品属性 -->
  <GoodsAttr v-if="eventType === 'Device'" :deviceAttributes="goodsAttributes" @on-callback="actionCallback" />
  <!-- 标注属性 -->
  <LabelAttr v-if="eventType === 'Annotation'" :attrs="labelAttributes" @on-callback="actionCallback" />
</template>

<script setup>
  import { ref, watch } from 'vue';
  import SpaceAttr from './SpaceAttr.vue';
  import GoodsAttr from './GoodsAttr.vue';
  import LabelAttr from './LabelAttr.vue';
  import { useDeviceAttributesStore } from '@/store/modules/deviceAttributes';

  const deviceAttributesStore = useDeviceAttributesStore();

  const emit = defineEmits(['onAction']);
  const eventType = ref('');
  const attributeList = ref([]);
  const goodsAttributes = ref({});
  const spaceAttributes = ref({});
  const labelAttributes = ref({});

  const actionCallback = (data) => {
    emit('onAction', data);
  };

  watch(
    () => deviceAttributesStore.attributeInfo,
    (val) => {
      console.log('deviceAttributesStore.attributeInfo', val);
      eventType.value = val?.selectType || '';
      attributeList.value = val?.attributeList || [];
      if (eventType.value === 'Device') {
        goodsAttributes.value = val?.attribute || {};
      } else if (eventType.value === 'Space') {
        console.log('空间属性', val);
        spaceAttributes.value = val?.attribute || {};
      } else if (eventType.value === 'Annotation') {
        labelAttributes.value = val?.attribute || {};
      }
    },
    { deep: true, immediate: true }
  );
</script>

<style scoped lang="stylus">
  .attr-panel
    width 240px
    background-color #f7f8fa
    padding 16px
    height 100%
    box-sizing border-box
    color #333
    font-size 13px

  .main-title
    font-size 14px
    font-weight 600
    padding-bottom 12px
    margin-bottom 12px
    border-bottom 1px solid #e5e5e5

  .product-info-section
    display flex
    align-items center
    background-color #fff
    padding 8px
    border-radius 4px
    margin-bottom 20px
    border 1px solid #e5e5e5

    .product-image
      width 40px
      height 40px
      margin-right 12px
      background-color #d3e4f4 // Placeholder color
      display flex
      align-items center
      justify-content center

    .product-text
      flex-grow 1
      .product-title
        font-size 13px
        font-weight 500
      .product-subtitle
        font-size 12px
        color #888

    .product-icon
      font-size 18px
      color #666
      cursor pointer

  .param-group
    padding-top 12px
    border-top 1px solid #e5e5e5

    .group-title
      font-size 14px
      font-weight 500
      margin-bottom 12px
    .control-row
      margin-bottom 10px
      .control-label
        color #555
      .control-input
        width 100%
        :deep(.ant-input-number-group-addon)
          padding 0 8px
          background-color #fff
        .unit-span
          color #888

  .action-section
    margin-top 20px
    margin-bottom 16px
    padding-top 16px
    border-top 1px solid #e5e5e5

  .add-accessory-btn
    display flex
    flex-direction column
    align-items center
    justify-content center
    width 60px
    height 60px
    border 1px dashed #d9d9d9
    border-radius 4px
    cursor pointer
    color #888
    transition all 0.2s
    &:hover
      border-color #1890ff
      color #1890ff
    span
      font-size 12px
      margin-top 4px
</style>
