<template>
  <a-modal
    v-model:open="open"
    title="添加配件"
    :maskClosable="false"
    :width="824"
    @cancel="handleCancel"
    @ok="handleConfirm">
    <div class="accessory-item-wrap">
      <div v-if="isSkeleton" class="product-skeleton">
        <div
          v-for="(product, index) in skeletonList"
          :key="product + index"
          class="product-item"
          :class="{ marginRight: (index + 1) % 6 !== 0 }">
          <product-skeleton></product-skeleton>
        </div>
      </div>
      <div
        v-else-if="!isSkeleton && list?.length > 0"
        class="accessory-item"
        v-for="accessory in list"
        :key="accessory.componentCode">
        <goods-card
          :goodsObj="accessory"
          :showCountIcon="true"
          :showSelectIcon="true"
          :isSelect="isSelectedAccessory(accessory)"
          @on-click="handleClick"
          @on-cancel="handleCancelSelect"
          @count-change="handleCountChange"></goods-card>
      </div>
      <div v-else class="product-empty">
        <div class="text-center">
          <img src="@/assets/images/list_empty.png" alt="" />
          <div class="no-product">暂无配件</div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref } from 'vue';
  import { _API_getAccessoryTree } from '@/api/deviceApi';
  import GoodsCard from '@/components/goodsCard/goodsCard.vue';
  import { isArray } from '@/utils/isType';
  import ProductSkeleton from '@/components/productSkeleton/productSkeleton.vue';

  const props = defineProps({
    componentCode: {
      type: String,
      default: '',
    },
    accessories: {
      type: Array,
      default: () => [],
    },
  });

  const emit = defineEmits(['cancel', 'confirm']);
  const selectedAccessorys = ref([]);

  const open = ref(true);
  const list = ref([]);

  const isSkeleton = ref(true);
  const skeletonList = [1, 2, 3, 4, 5, 6];

  const handleCancel = () => {
    emit('cancel');
  };
  const handleConfirm = () => {
      emit('confirm', selectedAccessorys.value);
  };

  const handleClick = (accessory) => {
    selectedAccessorys.value.push(accessory);
  };

  const handleCancelSelect = (accessory) => {
    selectedAccessorys.value = selectedAccessorys.value.filter(
      (item) => item.componentCode !== accessory.componentCode
    );
  };

  const isSelectedAccessory = (accessory) => {
    return selectedAccessorys.value.some((item) => item.componentCode === accessory.componentCode);
  };

  const handleCountChange = ({ componentCode, count }) => {
    const item = selectedAccessorys.value.find((acc) => acc.componentCode === componentCode);
    if (item) {
      item.count = count;
    }
  };

  const getAccessoryList = () => {
    _API_getAccessoryTree(props.componentCode)
      .then(
        (res) => {
          const { code, data } = res || {};
          if (code === '0') {
            if (isArray(data)) {
              const apiData = data.map((item) => ({
                count: 1,
                ...item,
              }));

              // 获取 accessories 中的 componentCode 列表
              if (Array.isArray(props.accessories) && props.accessories.length > 0) {
                // 已经添加的配件默认选中
                selectedAccessorys.value = props.accessories.map((item) => ({
                  ...item
                }))

                const existingCodes = props.accessories.map((item) => item.componentCode);
                // 过滤掉 apiData 中已存在于 accessories 的元素
                const filteredApiData = apiData.filter((item) => !existingCodes.includes(item.componentCode));

                // 将 accessories 中的元素排在前面，过滤后的 apiData 排在后面
                const sortedList = [...props.accessories, ...filteredApiData];
                list.value = sortedList;
              } else {
                list.value = apiData;
              }
            }
          }
        },
        (err) => {
          console.log(err);
        }
      )
      .finally(() => {
        isSkeleton.value = false;
      });
  };

  getAccessoryList();
</script>

<style scoped lang="stylus">
  .accessory-item-wrap {
   height: 430px;
   width: 100%;
   overflow: auto;
   display: flex;
   flex-wrap: wrap;
   gap: 12px;
   margin-top: 16px;
  }

  .accessory-item {
    width: 120px;
    height: 202px;
  }

  .product-skeleton {
    width: 100%;
    text-align center;
    padding 20px;
    display flex;
    gap 12px;
  }

  .product-empty {
    flex: 1;
    height: 400px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    .text-center {
      text-align center;
    }
    .no-product {
      color: rgba(0, 0, 0, 0.45);
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
</style>
