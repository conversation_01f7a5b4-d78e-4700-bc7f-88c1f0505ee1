<template>
  <div class="accessory-item">
    <div class="accessory-image">
      <client-img :src="accessory.mainImage" :alt="accessory.image" :preview="false" />
    </div>
    <div class="accessory-info">
      <div class="accessory-title">
        <div class="name text-hidden">{{ accessory?.componentDesc }}</div>
        <div class="accessory-delete">
          <Icon icon="synDelete" :size="14" pointer @click="handleDeleteAccessory(accessory.componentCode)" />
        </div>
      </div>
      <div class="accessory-size">
        {{ size }}
      </div>
      <div class="accessory-price-quantity">
        <div class="accessory-price">
          <Price :price="accessory.pcPrice" :styleObj="{ color: '#FF2121', size: '13px', smallSize: '10px' }"></Price>
        </div>
        <div class="accessory-quantity">
          <a-input-number
            v-model:value="accessory.count"
            @change="handleNumberChange"
            :min="1"
            size="small"
            class="count-input" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue';
  import ClientImg from '@/components/clientImg/clientImg.vue';
  import Price from '@/components/price/price.vue';
  import Icon from '@/components/icon/icon.vue';

  const props = defineProps({
    accessory: { type: Object, required: true },
  });
  const emit = defineEmits(['update:quantity', 'delete']);
  const handleNumberChange = (value) => {
    emit('update:quantity', { componentCode: props.accessory.componentCode, count: value });
  };

  const handleDeleteAccessory = (componentCode) => {
    emit('delete', componentCode);
  };

  const size = computed(() => {
    const { widthSize, depthSize, heightSize } = props.accessory;
    return widthSize && depthSize && heightSize ? `${widthSize}*${depthSize}*${heightSize}` : '暂无尺寸信息';
  });
</script>

<style scoped lang="stylus">
  .accessory-item
    display flex
    align-items center
    background #fff
    border 1px solid #e8e8e8
    border-radius 12px
    box-shadow 0 2px 8px rgba(0,0,0,0.04)
    padding 8px
    margin-bottom 16px
    width 100%

  .accessory-image
    width 64px
    height 64px
    margin-right 8px
    background-color #f0f2f5
    border-radius 6px
    flex none
    display flex
    align-items center
    justify-content center

  .accessory-info
    flex 1
    display flex
    flex-direction column
    justify-content center
    overflow hidden
    .accessory-title
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size 12px
      font-weight 500
      color var(--opn-text-main)
      margin-bottom 4px
      .name 
        flex: 1
    
    .accessory-size
      font-size 10px
      font-weight 400
      color rgba(0, 0, 0, 0.45)
      margin-bottom 8px
    .accessory-price-quantity
        display flex
        align-items: center
        justify-content: space-between;
        width: 100%
    .accessory-price
      font-size 18px
      color #ff2d2d
      font-weight bold

  .accessory-quantity
    display flex
    align-items center
    justify-content space-between
    .quantity-input
      width 60px

   .count-input
    margin-top 3px
    width 50px
    height 18px

  .accessory-delete
    width 14px
    font-size: 14px;
    color: var(--d9-color)
    flex: none
    &:hover
      color: var(--opn--hover-color)
    &:active
      color: #99541F

  :deep(.ant-input-number-focused)
     border-color var(--d9-color)

  :deep(input.ant-input-number-input)
     color var(--color-00085)
     font-size 10px
     font-weight 400
     height: 18px
     line-height 18px

  :deep(.ant-input-number:hover)
    border-color: var(--d9-color)
</style>
