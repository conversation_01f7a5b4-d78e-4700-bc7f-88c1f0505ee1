<template>
  <div class="attr-panel" ref="panelRef">
    <!-- 主标题 -->
    <div class="main-title-wrapper">
      <div class="main-title">商品属性</div>
      <div class="replace-block" @click="showProductModal">
        <span class="replace-icon">
          <Icon icon="synChange" :size="14" pointer />
        </span>
        <span class="text">替换</span>
      </div>
    </div>

    <!-- 商品信息 -->
    <div class="product-info-section">
      <div class="product-image">
        <client-img :src="deviceAttributes.mainImage" :preview="false" alt="product-image" />
      </div>

      <div class="product-text" >
        <div class="product-title" v-ellipsis-tooltip>{{ props.deviceAttributes.componentDesc }}</div>
        <div class="product-subtitle">{{ props.deviceAttributes.systemName }}</div>
      </div>
    </div>

    <!-- 参数组 -->
    <div class="group-title">尺寸</div>
    <a-row v-for="(control, cIndex) in attributeList" :key="cIndex" class="control-row" align="middle">
      <a-col :span="8" class="control-label">{{ control.label }}</a-col>
      <a-col :span="16" style="text-align: right">
        <a-input-number
          v-model:value="control.value"
          size="small"
          :disabled="control.disabled"
          :class="control.disabled ? 'disabled' : ''"
          class="control-input"
          style="width: 100px">
          <template v-if="control.unit" #addonAfter>
            <span class="unit-span">{{ control.unit }}</span>
          </template>
        </a-input-number>
      </a-col>
    </a-row>
  </div>

  <!-- 替换商品list面板 -->
  <product-model
    v-model="productModalVisible"
    title="商品替换"
    :selectedSystemId="deviceAttributes.systemId"
    :selectedSubModule="selectedSubModule"
    :selectComponentCodes="[deviceAttributes]"
    :showCountIcon="false"
    :showSelectIcon="false"
    @select-product="handleConfirmReplacement"
    @close="handleCancelReplacement" />

  <!-- 配件 -->
  <div class="accessory-group" v-if="deviceAttributes.partStatus">
    <div class="main-title-wrapper">
      <div class="main-title">配件</div>
      <div class="replace-block" @click="showAccessoryModal = true">
        <span class="replace-icon">
          <Icon icon="synAdd" pointer />
        </span>
        <span class="text">添加配件</span>
      </div>
    </div>

    <AccessoryItem
      v-for="acc in accessories"
      :key="acc.componentCode"
      :accessory="acc"
      @delete="deleteAccessory"
      @update:quantity="updateAccessoryQuantity" />
  </div>

  <!-- 配件面板 -->
  <AccessoryPanel
    v-if="showAccessoryModal"
    :componentCode="deviceAttributes.componentCode"
    :accessories="accessories"
    @cancel="showAccessoryModal = false"
    @confirm="addAccessory"></AccessoryPanel>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import ProductModel from '@/components/productModel/productModel.vue';
  // import ProductReplacementPanel from './ProductReplacementPanel.vue';
  import AccessoryItem from './AccessoryItem.vue';
  import AccessoryPanel from './accessoryPanel.vue';
  import ClientImg from '@/components/clientImg/clientImg.vue';
  import { message } from 'ant-design-vue';
  import Icon from '@/components/icon/icon.vue';

  // const deviceAttributesStore = useDeviceAttributesStore();
  const panelRef = ref(null);

  const props = defineProps({
    // 设备属性
    deviceAttributes: {
      type: Object,
      default: () => ({
        accessories: [],
        attributeList: [],
      }),
    },
  });

  const emit = defineEmits(['on-callback']);

  const accessories = ref([]); // 配件信息
  const attributeList = ref([]);
  const selectedSubModule = ref({
    type: 'room',
    subModuleId: '',
  });

  const productModalVisible = ref(false);
  const showAccessoryModal = ref(false);
  // const emit = defineEmits(['onAction']);
  const showProductModal = () => {
    productModalVisible.value = true;
  };

  // 配件数据
  // const accessories = ref([
  //   { id: 101, title: 'XXXX轨道框架', subtitle: '框架', image: 'https://dummyimage.com/40x40/f0f2f5/aaa', quantity: 1 },
  // ]);

  const handleCancelReplacement = () => {
    productModalVisible.value = false;
  };

  const handleConfirmReplacement = (selectedArr) => {
    const newProduct = selectedArr && selectedArr.length > 0 ? selectedArr[0] : null;
    if (newProduct.componentCode !== props.deviceAttributes.componentCode) {
      const data = {};
      data.action = 'replaceDevice';
      data.payload = {
        uuid: props.deviceAttributes.uuid,
        device: { ...newProduct },
      };
      emit('on-callback', data);
    } else {
      // 提示：
      message.error('替换的商品与原商品一致，替换失败');
    }
  };

  // 配件相关方法
  const addAccessory = (newAccessorys) => {
    // 首先检查accessories是否含有newAccessorys数组里的元素，如果有则更新数量，如果没有，则添加到accessories里
    if (Array.isArray(newAccessorys) && newAccessorys.length > 0) {
      accessories.value = [...newAccessorys];
    } else {
      accessories.value = [];
    }

    emit('on-callback', {
      action: 'setAccessory',
      payload: {
        deviceUuid: props.deviceAttributes.uuid,
        accessories: accessories.value,
      },
    });
    showAccessoryModal.value = false;
  };

  const deleteAccessory = (componentCode) => {
    accessories.value = accessories.value.filter((a) => a.componentCode !== componentCode);

    emit('on-callback', {
      action: 'setAccessory',
      payload: {
        deviceUuid: props.deviceAttributes.uuid,
        accessories: accessories.value,
      },
    });
  };

  const updateAccessoryQuantity = ({ componentCode, count }) => {
    const accessory = accessories.value.find((a) => a.componentCode === componentCode);
    if (accessory) {
      accessory.count = count;
    }
    emit('on-callback', {
      action: 'setAccessory',
      payload: {
        deviceUuid: props.deviceAttributes.uuid,
        accessories: accessories.value,
      },
    });
  };

  watch(
    () => props.deviceAttributes,
    (val) => {
      console.log('deviceAttributes', val);
      if (val) {
        attributeList.value = val?.attributeList?.[0]?.list || [];
        accessories.value = val?.accessories || [];
        selectedSubModule.value.subModuleId = val.baseSubModuleId;
      }
    },
    {
      immediate: true,
      deep: true,
    }
  );
</script>

<style scoped lang="stylus">
  .attr-panel
    margin 20px 16px
    box-sizing border-box
    font-size 13px
    border-bottom 1px solid #f0f0f0

   .main-title-wrapper
      display: flex
      align-items: center
      justify-content: space-between;
      margin-bottom: 12px
      .replace-block
        flex: 1
        display flex
        align-items center
        justify-content end
        color var(--opn--primary-color)
        &:hover
            color var(--opn--hover-color)
          &:active
            color #99541F

        .replace-icon
          width 14px
          font-size: 14px;
          margin-right 3px
          flex none

        .text
          font-size 12px
          font-style normal
          font-weight 400
          cursor pointer

      .main-title
        font-size 14px
        font-weight 500
        line-height 22px

    .group-title
      font-size 14px
      font-weight 500
      margin-bottom 14px

  .product-info-section
    display flex
    align-items center
    background-color #fff
    padding 8px
    border-radius 10px
    margin-bottom 20px
    border 1px solid #D9D9D9
    cursor pointer
    .product-image
      width 64px
      height 64px
      border-radius 6px
      background #F8F7F8
      margin-right 8px
      flex none
      display flex
      align-items center
      justify-content center

    .product-text
      height 64px
      padding: 5px 0
      flex-grow 1
      display flex
      flex-direction column
      justify-content space-between
      overflow hidden
      .product-title
        font-size 12px
        font-weight 500
      .product-subtitle
        font-size 10px
        font-weight 400
        color rgba(0, 0, 0, 0.45)

    .product-icon
      font-size 18px
      color #666
      cursor pointer

  .accessory-group
    // padding-top 20px
    margin 20px 16px

  .control-row
    margin-bottom 12px
    .control-label
      color var(--color-00085)
      font-size 12px;
      font-style normal
      font-weight 400
      line-height 20px
    .control-input
      color var(--color-00085)
      font-size 12px
      font-weight 400
      :deep(.ant-input-number-group-addon)
        padding 5px 8px
        border-color rgba(0, 0, 0, 0.15)
        color rgba(0, 0, 0, 0.25)
        font-size 12px
        font-weight 400
        background-color #fff
      :deep(.ant-input-number-sm)
        border-right none
        color var(--color-00085)
        border-color rgba(0, 0, 0, 0.15)
        line-height: 24px
        background-color #fff
        // border-radius 6px
      :deep(.ant-input-number-input)
        height: 24px
      .unit-span
        color rgba(0, 0, 0, 0.25)

  .disabled
    :deep(.ant-input-number-group-addon)
      background-color #f5f5f5 !important
    :deep(.ant-input-number-sm)
      background-color #f5f5f5  !important

  .action-section
    margin-top 20px
    margin-bottom 16px
    padding-top 16px
    border-top 1px solid #e5e5e5

  .add-accessory-btn
    display flex
    flex-direction column
    align-items center
    justify-content center
    width 60px
    height 60px
    border 1px dashed #d9d9d9
    border-radius 4px
    cursor pointer
    color #888
    transition all 0.2s
    &:hover
      border-color #1890ff
      color #1890ff
    span
      font-size 12px
      margin-top 4px
</style>
