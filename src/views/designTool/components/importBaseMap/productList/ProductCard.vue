<script setup>
  import ClientImg from '@/components/clientImg/clientImg.vue';

  const props = defineProps({
    product: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    selected: {
      type: Boolean,
      default: false,
    },
    selectable: {
      type: Boolean,
      default: true,
    },
  });

  const emit = defineEmits(['click']);

  const handleClick = () => {
    emit('click', props.product);
  };
</script>

<template>
  <div
    class="product-card"
    :class="{
      'product-card--selected': selected,
      'product-card--selectable': selectable,
    }"
    :role="selectable ? 'button' : 'article'"
    :tabindex="selectable ? 0 : -1"
    :aria-label="selectable ? `选择产品: ${product.title || product.name}` : undefined"
    @click="handleClick"
    @keydown.enter="handleClick"
    @keydown.space.prevent="handleClick">
    <div class="product-card__image">
      <client-img :src="product.imageUrl" :preview="false" fit="cover" />
    </div>
    <div class="product-card__content">
      <a-tooltip>
        <template #title>{{ product.name }} {{ product.spacsInfo }}</template>
        <div class="product-card__title">{{ product.name }} {{ product.spacsInfo }}</div>
      </a-tooltip>
      <div class="product-card__description">{{ product.srcArea }} | {{ product.specsInfo }}</div>
    </div>
  </div>
</template>

<style scoped lang="stylus">
  .product-card
    background-color #FAFAFA
    border-radius 10px
    padding 8px 12px
    display flex
    flex-direction column
    gap 8px
    cursor pointer
    transition all 0.3s ease
    border 1px solid transparent
    overflow hidden
    height 230px

    &:hover
      border-color var(--opn--hover-color)
      border-width 1.5px
      box-shadow 0 0 0 4px rgba(255, 251, 240, 1)

    &:focus
      outline none

    &--selectable:focus
      border-color var(--opn--primary-color)
      border-width 1.5px
      box-shadow 0 0 0 4px rgba(255, 251, 240, 1)

    &--selected
      border-color var(--opn--primary-color)
      border-width 1.5px
      box-shadow 0 0 0 4px rgba(255, 251, 240, 1)

    &__image
      flex-shrink 0
      height 160px
      width 100%
      background-color #FFFFFF
      border-radius 8px
      overflow hidden

    &__content
      flex 1
      display flex
      flex-direction column
      gap 4px
      min-height 0

    &__title
      font-family 'PingFang SC'
      font-weight 500
      font-size 14px
      line-height 1.5714285714285714
      color rgba(0, 0, 0, 0.85)
      @extends .ellipsis
      word-break break-word

    &__description
      font-family 'PingFang SC'
      font-weight 400
      font-size 12px
      line-height 1.6666666666666667
      color rgba(0, 0, 0, 0.85)
      @extends .ellipsis
      word-break break-word
</style>
