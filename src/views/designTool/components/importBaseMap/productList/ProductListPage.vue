<script setup>
  import { ref, computed, h, toRaw, nextTick } from 'vue';
import { message } from '@syn/ant-design4-vue3';
import { SearchOutlined } from '@ant-design/icons-vue';
import AddressSelect from '@/components/addressSelect/addressSelect.vue';
import { _API_searchHouse } from '@/api/comm.api';
  import { useUserStore } from '@/store/modules/userStore';
import { isArray } from '@/utils/isType';
import { randomId } from '@/utils/tools';
import listEmpty from '@/assets/images/list_empty.png';
import ProductCard from './ProductCard.vue';
import RulerTool from '../rulerTool/index.vue';

const emit = defineEmits(['productSelect', 'change']);
const userStore = useUserStore();

// 视图切换状态
const currentView = ref('productList'); // 'productList' | 'rulerTool'
const selectedProduct = ref(null);
const addressArr = ref([]);
const addressCodes = ref([]);

// 产品列表相关状态
const products = ref([]);
const searchQuery = ref('');
const cityName = ref('');
const selectedProductId = ref('');
const searchLoading = ref(false);

function handleAddressChange({ options }) {
  addressArr.value = options;
  cityName.value = options[1]?.region_name;
}

const getAddressInfo = () => {
  // const customerInfo = {
  //   "cityCode" : 110100,
  //   "cityName" : "北京",
  //   "provinceCode" : 110000,
  //   "provinceName": "北京",
  //   "regionCode" : 110105,
  //   "regionName": "朝阳区",
  //   "streetCode" : 110105001000,
  //   "streetName": "建外街道",
  // };
  const { customerInfo } = userStore.pageParams;
  // 为了方便测试 默认加载数据
  if (customerInfo && customerInfo.cityCode) {
    nextTick(() => {
      addressCodes.value = [`${customerInfo.provinceCode}`, `${customerInfo.cityCode}`];
      cityName.value = customerInfo.cityName;
      searchQuery.value = customerInfo.streetName;
      handleSearchHouse();
    });
  }
}

const handleSearchHouse = () => {
    if (!cityName.value || !searchQuery.value) {
      message.warn('请选择地区和小区');
      return;
    }
    searchLoading.value = true;
    _API_searchHouse({
      cityName: `${cityName.value}市`,
      commName: searchQuery.value,
      keyWord: searchQuery.value,
      pageNo: 1,
      pageSize: 100,
    })
      .then((res) => {
        console.log('户型结果数据', res);
        if (isArray(res?.data?.records)) {
          products.value = res?.data?.records.map((item) => ({
            ...item,
            id: randomId(),
          }));
        }
      })
      .finally(() => {
        searchLoading.value = false;
        console.log("searchLoading.value", searchLoading.value);
      });
};

const handleProductClick = (product) => {
  selectedProductId.value = selectedProductId.value === product.id ? '' : product.id;
  selectedProduct.value = product;

  // 选择产品后自动切换到尺子工具
  currentView.value = 'rulerTool';

  emit('productSelect', product);
};

const isProductSelected = (product) => {
  return selectedProductId.value === product.id;
};

// 处理尺子工具数据变化 searchQuery.value
const handleRulerChange = (data) => {
  emit('change', data, {
    ...toRaw(addressArr.value),
    street: searchQuery.value,
  });
};

const handleCloseRuler = () => {
  currentView.value = 'productList'
}

// 计算属性 - 使用 visible 控制显示隐藏
const showProductList = computed(() => currentView.value === 'productList');
const showRulerTool = computed(() => currentView.value === 'rulerTool');

// Generate skeleton items for loading state
const skeletonItems = computed(() => Array(8).fill(null));

getAddressInfo();
</script>

<template>
  <div class="product-list-page">
    <!-- 视图容器 -->
    <div class="view-container">
      <!-- 产品列表视图 - 使用 v-show 保持状态 -->
      <div v-show="showProductList" class="product-list-view">
        <!-- Search Section -->
        <div class="search-section">
          <div class="address-select-container">
            <AddressSelect :fourCodes="addressCodes" placeholder="请选择" :max="2" @onChange="handleAddressChange" />
          </div>
          <div class="search-input-container">
            <a-input
              v-model:value="searchQuery"
              placeholder="搜索内容"
              allow-clear
              @pressEnter="handleSearchHouse"
            />
          </div>
          <div class="search-button-container">
            <a-button type="primary" :icon="h(SearchOutlined)" @click="handleSearchHouse" :loading="searchLoading">搜索</a-button>
          </div>
        </div>

        <!-- Product Grid -->
        <div class="product-grid-container">
          <!-- Loading State -->
          <div v-if="searchLoading" class="product-grid">
            <div v-for="(_, index) in skeletonItems" :key="`skeleton-${index}`" class="skeleton-card">
              <a-skeleton active>
                <template #title>
                  <div class="skeleton-image"></div>
                </template>
                <template #paragraph>
                  <div class="skeleton-content">
                    <div class="skeleton-title"></div>
                    <div class="skeleton-description"></div>
                  </div>
                </template>
              </a-skeleton>
            </div>
          </div>

          <!-- Empty State -->
          <div v-else-if="!products.length" class="empty-state">
            <a-empty
              :image="listEmpty"
              :image-style="{
                width: '80px',
                height: '80px',
                objectFit: 'contain',
              }"
            >
              <template #description>
                <span class="empty-text">
                  暂无搜索结果
                </span>
              </template>
            </a-empty>
          </div>

          <!-- Product Grid -->
          <div v-else class="product-grid">
            <product-card
              v-for="product in products"
              :key="product.id"
              :product="product"
              :selected="isProductSelected(product)"
              :selectable="true"
              @click="handleProductClick"
            />
          </div>
        </div>
      </div>

      <!-- 尺子工具视图 - 使用 v-show 保持状态 -->
      <div v-show="showRulerTool" class="ruler-tool-view">
        <RulerTool
          :showUpload="false"
          :imageUrl="selectedProduct?.imageUrl || ''"
          @close="handleCloseRuler"
          @change="handleRulerChange"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="stylus">
.product-list-page {
  display: flex;
  flex-direction: column;
  height: 100%;
}

// 视图容器样式优化
.view-container {
  flex: 1;
  overflow: hidden;
  position: relative;
}

// 产品列表视图样式
.product-list-view {
  background: white;
}

// 尺子工具视图样式
.ruler-tool-view {
  background: white;
}

.search-section {
  display: flex;
  align-items: stretch;
  height: 40px;
  margin-bottom: 16px;

  .address-select-container {
    width: 170px;

    :deep(.ant-cascader) {
      width: 100%;
      height: 40px;

      .ant-select-selector {
        height: 40px !important;
        padding: 8px 12px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 8px 0 0 8px !important;
        font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 16px;
        font-weight: 400;
        line-height: 1.5;

        .ant-select-selection-item {
          color: rgba(0, 0, 0, 0.85);
          line-height: 24px;
        }
        .ant-select-selection-search-input {
          height: 100%;
        }

        .ant-select-selection-placeholder {
          color: rgba(0, 0, 0, 0.25);
          line-height: 24px;
        }

        .ant-select-arrow {
          color: rgba(0, 0, 0, 0.25);

          .anticon {
            font-size: 12px;
          }
        }
      }

      &:hover .ant-select-selector {
        border-color: var(--opn--hover-color);
      }

      &:focus .ant-select-selector,
      &.ant-select-focused .ant-select-selector {
        border-color: var(--opn--primary-color);
        box-shadow: none;
      }
    }
  }

  .search-input-container {
    display: flex
    flex-direction: row;
    flex: 1;
    :deep(.ant-input-affix-wrapper) {
      position: relative;
      padding 0;
      border unset;
      margin 0;
      .ant-input-suffix {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        z-index: 10;
        padding: 0 8px;
      }
    }
    :deep(.ant-input) {
      height: 40px;
      padding: 8px 12px;
      background: #FFFFFF;
      border: 1px solid rgba(0, 0, 0, 0.15);
      border-radius: 0;
      border-left: 0;
      border-right: 0;
      font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 400;
      line-height: 1.5714285714285714;

      &::placeholder {
        color: rgba(0, 0, 0, 0.25);
        font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.5714285714285714;
      }

      &:hover {
        border-color: var(--opn--hover-color);
        border-left: 0;
        border-right: 0;
      }

      &:focus {
        border-color: var(--opn--primary-color);
        border-left: 0;
        border-right: 0;
        box-shadow: none;
      }
    }
  }


  .search-button-container {
    :deep(.ant-btn) {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 84px;
      height: 40px;
      background: #000000;
      border: 1px solid #000000;
      border-radius: 0 6px 6px 0;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #D9BA7C;
      .anticon {
        color: #D9BA7C;
        font-size: 14px;
      }
    }


    &:hover {
      background: #D9BA7C;
      border-color: #000000;
    }

    &:active {
      background: #D9BA7C;
      border-color: #000000;
    }
  }
}

.product-grid-container {
  overflow-y: auto;
  overflow-x: hidden;
  height: 432px;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px 16px;
  padding: 5px;
}

.skeleton-card {
  background-color: #FAFAFA;
  border-radius: 10px;
  padding: 8px 12px;
  height: 230px;

  .skeleton-image {
    height: 160px;
    background-color: #F0F0F0;
    border-radius: 8px;
    margin-bottom: 8px;
  }

  .skeleton-content {
    .skeleton-title {
      height: 20px;
      background-color: #F0F0F0;
      border-radius: 4px;
      margin-bottom: 4px;
    }

    .skeleton-description {
      height: 16px;
      background-color: #F0F0F0;
      border-radius: 4px;
      width: 80%;
    }
  }
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  :deep(.ant-empty-description) {
    margin: 0;
  }
  .empty-text {
    font-size: 14px;
    font-family: PingFang SC;
    font-weight: 400;
    line-height: 22px;
    color: rgba(0, 0, 0, 0.45);
  }
}

// Responsive adjustments
@media (max-width: 1200px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px 12px;
  }
}

@media (max-width: 480px) {
  .product-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}
</style>
