<script setup>
  import { ref, computed } from 'vue';
  import { CREATE_QUOTATION_STATE, useGlobalModalStore } from '@/store/modules/globalModalStore';
  import { _API_getQuotePreview, _API_quoteSave } from '@/api/intelligentApi';
  import { uniqueArrayByKey } from '@/utils/tools';
  import { message } from '@syn/ant-design4-vue3';
  import { useUserStore } from '@/store/modules/userStore';
  const globalModalStore = useGlobalModalStore();
  const userStore = useUserStore();
  const open = ref(true);
  const confirmLoading = ref(false);
  const props = defineProps({
    title: {
      type: String,
      default: '报价单',
    },
    code: {
      type: String,
      default: '',
    }
  });
  const tabs = ref([
    {
      title: "按空间",
      value: "space",
    },
    {
      title: "按智慧系统",
      value: "system",
    },
  ]);
  const activeMainTab = ref('space');
  const activeSubTab = ref('all');

  // 缓存好原始数据
  const originData = ref({
    roomList: [],
    systemList: [],
  });
  const spaces = ref([]);
  const systems = ref([]);

  // 合计
  function calcAmount(item) {
    return item.price * item.qty;
  }
  function calcDiscountAmount(item) {
    if (item.disPrice !== undefined && item.disPrice !== null) {
      return item.disPrice * item.qty;
    }
    return props.discount ? item.price * props.discount * item.qty : calcAmount(item);
  }

  const columns = [
    {
      title: '空间',
      dataIndex: 'space',
      key: 'space',
      width: 100,
      customCell: (record, rowIndex) => {
        const rowspan = spaceRowspanMap.value[rowIndex] || 0;
        if (record._isServiceFee) return { rowSpan: 1 };
        return { rowSpan: rowspan };
      },
    },
    {
      title: '智慧系统',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      customCell: (record, rowIndex) => {
        if (record._isServiceFee) return { colSpan: 5 };
        const rowspan = secondRowspanMap.value[rowIndex] || 0;
        return { rowSpan: rowspan };
      },
    },
    {
      title: '商品',
      dataIndex: 'product',
      key: 'product',
      customCell: (record) => {
        if (record._isServiceFee) return { colSpan: 0 };
        return {};
      },
    },
    {
      title: '编码',
      dataIndex: 'code',
      key: 'code',
      customCell: (record) => {
        if (record._isServiceFee) return { colSpan: 0 };
        return {};
      },
    },
    {
      title: '单价',
      dataIndex: 'price',
      key: 'price',
      customCell: (record) => {
        if (record._isServiceFee) return { colSpan: 0 };
        return {};
      },
    },
    {
      title: '数量',
      dataIndex: 'qty',
      key: 'qty',
      customCell: (record) => {
        if (record._isServiceFee) return { colSpan: 0 };
        return {};
      },
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 100,
      customCell: (record) => {
        if (record._isServiceFee) return { colSpan: 2 };
        return {};
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      customCell: (record, rowIndex) => {
        if (record._isServiceFee) return { colSpan: 0 };
        const rowspan = secondRowspanMap.value[rowIndex] || 0;
        return { rowSpan: rowspan };
      },
    },
  ];
  const tableData = ref([
    {
      space: '客厅',
      totalPrice: 800,
      disPrice: 700,
      children: [
        {
          category: '智控',
          code: '734673564',
          remark: '备注文案',
          children: [
            { product: '灯简1', code: '734673564', price: 100, qty: 1 },
            { product: '灯简1', code: '734673564', price: 100, qty: 1 },
            { product: '灯简2', code: '734673564', price: 100, qty: 1 },
          ],
        },
        {
          category: '安防',
          code: '734673566',
          remark: '备注文案',
          children: [
            { product: '灯简1', code: '734673564', price: 100, qty: 1 },
            { product: '灯简1', code: '734673564', price: 100, qty: 1 },
            { product: '灯简2', code: '734673564', price: 100, qty: 1 },
          ],
        },
      ],
    },
    {
      space: '餐厅',
      totalPrice: 700,
      children: [
        {
          category: '智控',
          code: '734673564',
          remark: '备注文案',
          children: [
            { product: '灯简1', code: '734673564', price: 100, qty: 1 },
            { product: '灯简1', code: '734673564', price: 100, qty: 1 },
            { product: '灯简2', code: '734673564', price: 100, qty: 1 },
          ],
        },
        {
          category: '安防',
          code: '734673566',
          remark: '备注文案',
          children: [
            { product: '灯简1', code: '734673564', price: 100, qty: 1 },
            { product: '灯简1', code: '734673564', price: 100, qty: 1 },
            { product: '灯简2', code: '734673564', price: 100, qty: 1 },
          ],
        },
      ],
    },
  ]);

  const subTabs = computed(() => {
    // 根据类型处理二级分类
    return activeMainTab.value === 'space' ? spaces.value : systems.value;
  });
  // 监听主标签点击后，表格数据变化
  const filteredData = computed(() => {
    if (activeMainTab.value === 'space') {
      if (activeSubTab.value === 'all') return tableData.value;
      return tableData.value.filter((item) => item.space === activeSubTab.value);
    } else {
      if (activeSubTab.value === 'all') return tableData.value;
      return tableData.value.filter((item) => item.system === activeSubTab.value);
    }
  });

  // 拍平数据，记录空间、分类、商品的索引
  const SERVICE_FEE = 2999.0;
  const flatRows = computed(() => {
    const rows = [];
    filteredData.value.forEach((spaceItem, spaceIdx) => {
      spaceItem.children.forEach((catItem, catIdx) => {
        catItem.children.forEach((prod) => {
          rows.push({
            ...prod,
            space: spaceItem.space,
            totalPrice: spaceItem.totalPrice,
            disPrice: spaceItem.disPrice,
            remark: catItem.remark,
            category: catItem.category,
            _spaceIndex: spaceIdx,
            _categoryIndex: catIdx,
            _isServiceFee: false,
          });
        });
      });
    });
    // 添加服务费行
    rows.push({
      _isServiceFee: true,
      space: '服务费',
      category: '',
      product: '',
      code: '',
      price: '',
      qty: '',
      remark: '',
      serviceFeeDesc: '折扣仅影响商品价格，不影响服务费',
      serviceFee: SERVICE_FEE,
    });
    console.log("rows", rows);
    return rows;
  });

  // 计算每个空间、分类的起始行和rowspan
  const spaceRowspanMap = computed(() => {
    const map = {};
    let rowIndex = 0;
    filteredData.value.forEach((spaceItem) => {
      let count = 0;
      spaceItem.children.forEach((catItem) => {
        count += catItem.children.length;
      });
      map[rowIndex] = count;
      rowIndex += count;
    });
    return map;
  });
  // 计算每个分类、备注的起始行和rowspan
  const secondRowspanMap = computed(() => {
    const map = {};
    let rowIndex = 0;
    filteredData.value.forEach((spaceItem) => {
      spaceItem.children.forEach((catItem) => {
        map[rowIndex] = catItem.children.length;
        rowIndex += catItem.children.length;
      });
    });
    return map;
  });

  const totalOrigin = computed(() => flatRows.value.reduce((sum, item) => sum + calcAmount(item), 0));
  const totalDiscount = computed(() => flatRows.value.reduce((sum, item) => sum + calcDiscountAmount(item), 0));

  function handleOk() {
    confirmLoading.value = true;
    const { customerInfo = {} } = userStore.pageParams;
    _API_quoteSave({
      caseCode: props.code,
      designerShopCode: customerInfo.mdCode,
      designerShopName: customerInfo.mdName,
    }).then(res => {
      if (res["code"] === "0") {
        message.success('保存成功!');
        handleCancel();
      }
    }).finally(() => {
      confirmLoading.value = false;
    });
  }

  function handleCancel() {
    globalModalStore.clearStoreState(CREATE_QUOTATION_STATE);
  }

  function handleTabChange(tabKey) {
    console.log(tabKey);
    activeSubTab.value = 'all';
  }

  function generateTabData(arr, label, value) {
    const $arr = uniqueArrayByKey(arr.map(item => ({
      label: item[label],
      value: item[value],
    })), "value");
    return [
      {
        label: '全部',
        value: 'all',
      },
      ...$arr,
    ];
  }

  async function getData() {
    // ZK_250731002528
    console.log("code", props.code)
    const { code, data } = await _API_getQuotePreview("ZK_250731002528");
    console.log("res", data);
    if (code === "0") {
      originData.value = data;
      // 有数据后先初始化两个二级分类的数据
      spaces.value = generateTabData(data.roomList, 'roomName', 'roomTypeId');
      systems.value = generateTabData(data.systemList, 'systemName', 'systemId');
    }

  }

  getData();
</script>

<template>
  <a-modal
    v-model:open="open"
    :title="props.title"
    :maskClosable="false"
    :keyboard="false"
    destroy-on-close
    :width="970"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-tabs v-model:activeKey="activeMainTab" @change="handleTabChange">
      <a-tab-pane key="space" tab="按空间"></a-tab-pane>
      <a-tab-pane key="system" tab="按智慧系统"></a-tab-pane>
    </a-tabs>
    <a-tabs
      v-model:activeKey="activeSubTab"
    >
      <a-tab-pane v-for="tab in subTabs" :key="tab.value" :tab="tab.label" />
    </a-tabs>
    <a-table
      :dataSource="flatRows"
      :columns="columns"
      :pagination="false"
      :scroll="{ y: 400 }"
      :rowKey="(record) => record.code || record.space"
      bordered>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'space'">
          <div>{{ record.space }}</div>
          <template v-if="record.disPrice !== undefined && record.disPrice !== null">
            <div class="del-line">¥{{ record.totalPrice?.toFixed(2) }}</div>
            <div class="price">¥{{ record.disPrice?.toFixed(2) }}</div>
          </template>
          <template v-else>
            <div v-if="record.totalPrice !== undefined && record.totalPrice !== null">
              ¥{{ record.totalPrice?.toFixed(2) }}
            </div>
          </template>
        </template>
        <template v-else-if="column.dataIndex === 'qty'">*{{ record.qty }}</template>
        <template v-else-if="column.dataIndex === 'amount'">
          <div>¥{{ calcDiscountAmount(record).toFixed(2) }}</div>
        </template>
      </template>
    </a-table>
    <template #footer>
      <span style="margin-right: 24px">
        总价：
        <span v-if="totalDiscount !== totalOrigin">
          <span class="del-line">¥{{ totalOrigin.toFixed(2) }}</span>
          <span class="price">¥{{ totalDiscount.toFixed(2) }}</span>
        </span>
        <span v-else>¥{{ totalOrigin.toFixed(2) }}</span>
      </span>
      <span class="dialog-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk" :loading="confirmLoading">保存</a-button>
      </span>
    </template>
  </a-modal>
</template>

<style scoped lang="stylus">
  .button-container {
    margin-bottom: 12px;
    margin-top: 12px;
  }
  .del-line {
    text-decoration: line-through;
    color: #999;
  }
  .price {
    color: #ff4d4f;
  }
</style>
