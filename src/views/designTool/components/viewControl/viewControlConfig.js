export const createViewControl = async () => {
  return [
    {
      label: '显示设置',
      action: 'viewControl',
      icon: 'synDisplaySetting',
    },
    {
      label: '底图设置',
      action: 'floorImageSet',
      icon: 'synBaseMapSetting',
      children: [
        {
          label: '底图透明度',
          action: 'setFloorOpacity',
          icon: '',
        },
        {
          label: '删除底图',
          action: 'deleteFloorImage',
        },
      ],
    },
  ];
};

export const floorOpacityList = [
  {
    label: '0%',
    value: 0,
    action: 'setFloorOpacity',
  },
  {
    label: '25%',
    value: 0.25,
    action: 'setFloorOpacity',
  },
  {
    label: '50%',
    value: 0.5,
    action: 'setFloorOpacity',
  },
  {
    label: '75%',
    value: 0.75,
    action: 'setFloorOpacity',
  },
  {
    label: '100%',
    value: 1,
    action: 'setFloorOpacity',
  },
];
