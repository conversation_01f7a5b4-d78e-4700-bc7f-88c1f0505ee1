<script setup>
  import { onMounted, ref } from 'vue';
  import { useDeviceAttributesStore } from '@/store/modules/deviceAttributes';
  const emit = defineEmits(['action']);
  const deviceAttributesStore = useDeviceAttributesStore();
  const allButtons = [
    {
      label: '复制',
      action: 'copyDevice',
      show: () => {
        return true;
      },
    },
    {
      label: '替换',
      action: 'replaceDevice',
      show: () => {
        return true;
      },
    },
    {
      label: '删除',
      action: 'deleteDevice',
      show: () => {
        return true;
      },
    },
    {
      label: '标注',
      action: 'annotation',
      show: () => {
        return deviceAttributesStore?.attributeInfo?.attribute?.canSetAnnotation;
      },
    },
  ];
  const operateList = ref([]);

  /**
   * @description 点击操作
   * @param item
   */
  function handleClick(item) {
    emit('action', {
      ...item,
    });
  }

  onMounted(() => {
    operateList.value = allButtons.filter((item) => item.show());
  });
</script>

<template>
  <div class="operator-wrap">
    <div class="operator-item" v-for="item in operateList" :key="item.action" @click="handleClick(item)">
      {{ item.label }}
    </div>
  </div>
</template>

<style scoped lang="stylus">
  .operator-wrap
    background #fff
    display flex
    flex-direction column
    width 56px
    align-items center
    border-radius 8px
    padding 4px
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12), 0px 9px 28px 8px rgba(0, 0, 0, 0.05);
  .operator-item
    cursor pointer;
    padding 5px 12px
    line-height 20px
    width 49px
    @extends .content-small
    color rgba(0,0,0,0.85)
</style>
