export const productDemoList = [
  {
    uuid: 1,
    spaceName: '全屋',
    spaceId: 1,
    goods: [
      {
        uuid: 'B508X9000',
        componentCode: 'B508X9000',
        componentName: 'LC-200WLH9EC1',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      },
      {
        uuid: 'B508X6000',
        componentCode: 'B508X6000',
        componentName: 'LC-197WLC9ED1U1',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      },
      {
        uuid: 'B00V72000',
        componentCode: 'B00V72000',
        componentName: 'BCD-470WDCXU1(EX)',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      },
      {
        uuid: 'BC115M000',
        componentCode: 'BC115M000',
        componentName: 'BCD-466WGHTDEDC9',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      },
      {
        uuid: 'GA0SJ201C',
        componentCode: 'GA0SJ201C',
        componentName: 'LSA-C1白(LP)',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      },
      {
        uuid: 'FY001D009',
        componentCode: 'FY001D009',
        componentName: 'C5O60EGU1',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      },
      {
        uuid: 'CEACF5000',
        componentCode: 'CEACF5000',
        componentName: 'G100558HBD12S',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      },
      {
        uuid: 'B502R001T',
        componentCode: 'B502R001T',
        componentName: 'B502R001T-1',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      },
      {
        uuid: 'BC12N3000',
        componentCode: 'BC12N3000',
        componentName: 'BC12N3000-1',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      },
      {
        uuid: 'CE0JE301N',
        componentCode: 'CE0JE301N',
        componentName: 'CE0JE301N-1',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      },
      {
        uuid: 'AAABTS071',
        componentCode: 'AAABTS071',
        componentName: 'AAABTS071-1',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      }
    ],
    scene: [
      {
        sceneCode: 'MS000000109',
        sceneName: '智能营销测试',
        subModules: [
          {
            baseSubModuleId: '2127', //二级类目ID
            subModuleName: '遥控器',//二级类目名称
            goodIds: [
              {uuid: 'AAABTS071'}
            ]
          },
          {
            baseSubModuleId: '2126', //二级类目ID
            subModuleName: '支架',//二级类目名称
            goodIds: [
              {uuid: 'BC12N3000'},
              {uuid: 'CE0JE301N'}
            ]
          },
          {
            baseSubModuleId: '2123', //二级类目ID
            subModuleName: '控制面板',//二级类目名称
            goodIds: [
              {uuid: 'B502R001T'}
            ]
          }
        ]
      },
      {
        sceneCode: 'MS000000123',
        sceneName: '智慧场景1',
        subModules: []
      },
      {
        sceneCode: 'MS000000133',
        sceneName: '场景002',
        subModules: []
      }
    ]
  },
  {
    uuid: 2,
    spaceName: '客厅',
    spaceId: 2,
    goods: [
      {
        uuid: 'CE0JE301N',
        componentCode: 'CE0JE301N',
        componentName: 'CE0JE301N-1',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      },
      {
        uuid: 'AAABTS071',
        componentCode: 'AAABTS071',
        componentName: 'AAABTS071-1',
        mainImage: 'https://haier-ism.oss-cn-qingdao.aliyuncs.com/test/sceneMarket/MASTER/d5af06ea7ef346068c86464265ab5a92.jpg'
      }
    ],
    scene: [
      {
        sceneCode: 'MS000000109',
        sceneName: '智能营销测试',
        subModules: [
          {
            baseSubModuleId: '2127', //二级类目ID
            subModuleName: '遥控器',//二级类目名称
            goodIds: [
              {uuid: 'AAABTS071'}
            ]
          },
          {
            baseSubModuleId: '2126', //二级类目ID
            subModuleName: '支架',//二级类目名称
            goodIds: [
              {uuid: 'CE0JE301N'}
            ]
          },
        ]
      },
    ]
  },
];

export const spaceList = [
  {
    name: "房间1",
    center: [471.9925802475987, 500.65899100700636],
    path: [
      372.67042178156953,
      621.7612608240767,
      571.3147387136278,
      621.7612608240767,
      571.3147387136278,
      379.55672118993596,
      467.3545896157668,
      379.55672118993596,
      454.10341889261167,
      379.55672118993596,
      372.67042178156953,
      379.55672118993596,
      372.67042178156953,
      621.7612608240767
    ],
    roomId: "NeuEYXElEyznc-6vRu3Ib",
    roomType: 0,
    type: "Room",
    uuid: "NeuEYXElEyznc-6vRu3Ib"
  },
  {
    name: "未命名",
    center: [739.************, 448.71571666987256],
    path: [
      666.1221504630923,
      631.7612608240767,
      676.1221504630923,
      631.7612608240767,
      676.1221504630923,
      801.8613859032871,
      942.************,
      801.8613859032871,
      942.************,
      154.43160272845986,
      477.3545896157668,
      154.43160272845986,
      477.3545896157668,
      359.55672118993596,
      581.3147387136278,
      359.55672118993596,
      581.3147387136278,
      369.55672118993596,
      591.3147387136278,
      369.55672118993596,
      591.3147387136278,
      621.7612608240767,
      666.1221504630923,
      621.7612608240767,
      666.1221504630923,
      631.7612608240767
    ],
    roomId: "XB17wdVbiupg-JA8O4Kph",
    roomType: 0,
    type: "Room",
    uuid: "XB17wdVbiupg-JA8O4Kph",
  },
];
export const sceneProductDemoList = [{
  "uuid": "62vDIKiXsMDzGDLzTKT83",
  "type": "Furniture",
  "componentCode": "AAABTS071",
  "componentDesc": "KFR-26GW/06KAA81U1套机1匹雷神者挂机",
  "mainImage": "https://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/AAABTS071/productPicture/a78ff9fa9aaf4708885d705dfb5d8317.png",
  "widthSize": null,
  "depthSize": null,
  "heightSize": null,
  "systemId": "14",
  "systemName": "智控",
  "baseSubModuleId": "2127",
  "subModuleName": "遥控器",
  "partStatus": 0,
  "roomId": "NeuEYXElEyznc-6vRu3Ib",
  "position": [-281.97879858657257, -59.36395759717311],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 0
}, {
  "uuid": "Wpv77txxv3LtDWy-kRGbj",
  "type": "Furniture",
  "componentCode": "AAABTS071",
  "componentDesc": "KFR-26GW/06KAA81U1套机1匹雷神者挂机",
  "mainImage": "https://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/AAABTS071/productPicture/a78ff9fa9aaf4708885d705dfb5d8317.png",
  "widthSize": null,
  "depthSize": null,
  "heightSize": null,
  "systemId": "14",
  "systemName": "智控",
  "baseSubModuleId": "2127",
  "subModuleName": "遥控器",
  "partStatus": 0,
  "roomId": "NeuEYXElEyznc-6vRu3Ib",
  "position": [-334.************, -159.01060070671372],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 0
}, {
  "uuid": "J1StQ8iDAhPoR1qqiE73C",
  "type": "Furniture",
  "componentCode": "AAABTS071",
  "componentDesc": "KFR-26GW/06KAA81U1套机1匹雷神者挂机",
  "mainImage": "https://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/AAABTS071/productPicture/a78ff9fa9aaf4708885d705dfb5d8317.png",
  "widthSize": null,
  "depthSize": null,
  "heightSize": null,
  "systemId": "14",
  "systemName": "智控",
  "baseSubModuleId": "2127",
  "subModuleName": "遥控器",
  "partStatus": 0,
  "roomId": "NeuEYXElEyznc-6vRu3Ib",
  "position": [-72.08480565371019, -120.84805653710242],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 0
}, {
  "uuid": "3MmwdHRDAWZCry9ijS1Gu",
  "type": "Furniture",
  "componentCode": "CF067A005",
  "componentDesc": "HGY100-F386U1",
  "mainImage": "https://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/CF067A005/productPicture/92dcfed7efa94db19ba7da3997588627.png",
  "widthSize": null,
  "depthSize": null,
  "heightSize": null,
  "systemId": "34",
  "systemName": "智慧烘烤",
  "baseSubModuleId": "1633",
  "subModuleName": "c",
  "partStatus": 1,
  "roomId": "NeuEYXElEyznc-6vRu3Ib",
  "position": [-296.8197879858658, 65.72438162544177],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 4999
}, {
  "uuid": "V5XCPyP9-1dZkEm0iTNtO",
  "type": "Furniture",
  "componentCode": "BB0UZ0089",
  "componentDesc": "BCD-400WDYBU1",
  "mainImage": "https://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/BB0UZ0089/productPicture/ad89899129804b55b50d845a1595f38b.png",
  "widthSize": null,
  "depthSize": null,
  "heightSize": null,
  "systemId": "34",
  "systemName": "智慧烘烤",
  "baseSubModuleId": "1633",
  "subModuleName": "c",
  "partStatus": 1,
  "roomId": "NeuEYXElEyznc-6vRu3Ib",
  "position": [-16.96113074204959, 148.4098939929329],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 2899
}, {
  "uuid": "exhwjcl9qHGeg_bmEpiOO",
  "type": "Furniture",
  "componentCode": "BB0UZ0089",
  "componentDesc": "BCD-400WDYBU1",
  "mainImage": "https://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/BB0UZ0089/productPicture/ad89899129804b55b50d845a1595f38b.png",
  "widthSize": null,
  "depthSize": null,
  "heightSize": null,
  "systemId": "34",
  "systemName": "智慧烘烤",
  "baseSubModuleId": "1633",
  "subModuleName": "c",
  "partStatus": 1,
  "roomId": "NeuEYXElEyznc-6vRu3Ib",
  "position": [-245.93639575971747, 167.49116607773863],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 2899
}, {
  "uuid": "8n14cf1EZwFsD5BtQBP2W",
  "type": "Furniture",
  "componentCode": "DH1WVVA00",
  "componentDesc": "K75E60",
  "mainImage": "https://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/DH1WVVA00/productPicture/25198373ffaa4501b82e41ac5fbe8743.png",
  "widthSize": null,
  "depthSize": null,
  "heightSize": null,
  "systemId": "34",
  "systemName": "智慧烘烤",
  "baseSubModuleId": "1633",
  "subModuleName": "c",
  "partStatus": 0,
  "roomId": "NeuEYXElEyznc-6vRu3Ib",
  "position": [-417.6678445229684, 69.96466431095416],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 5999
}, {
  "uuid": "F6VyajE2RZ9-EzuK968Rq",
  "type": "Furniture",
  "componentCode": "GD0RDJ001",
  "componentDesc": "JSQ31-16FR5BDU1",
  "mainImage": "https://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/GD0RDJ001/productPicture/922c974de1fb4a9b86bbba422ac8bb70.png",
  "widthSize": "1",
  "depthSize": "2",
  "heightSize": "3",
  "systemId": "18",
  "systemName": "网络",
  "baseSubModuleId": "2084",
  "subModuleName": "摄像机",
  "partStatus": 1,
  "roomId": "NeuEYXElEyznc-6vRu3Ib",
  "position": [-390.10600706713785, -65.72438162544165],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 4998.3
}, {
  "uuid": "kQCGNldhsm6vy2PPbFHEq",
  "type": "Furniture",
  "componentCode": "B00XD0000",
  "componentDesc": "BCD-502WGHFD14SCU1",
  "mainImage": "https://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/B00XD0000/productPicture/f8a70e6e5e154b518fd3bd71a7b621c1.png",
  "widthSize": "12344",
  "depthSize": "544.55",
  "heightSize": "356.6766",
  "systemId": "34",
  "systemName": "智慧烘烤",
  "baseSubModuleId": "1633",
  "subModuleName": "c",
  "partStatus": 1,
  "roomId": "XB17wdVbiupg-JA8O4Kph",
  "position": [360.4240282685512, -91.16607773851587],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 5999
}, {
  "uuid": "vWZQKIdEWKHwankTzg9S8",
  "type": "Furniture",
  "componentCode": "FY001B007",
  "componentDesc": "C6S46BGU1",
  "mainImage": "http://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/FY001B007/productPicture/20240118135518668_7c50521ef39f34e7a7b879a375962c10.png",
  "widthSize": "21",
  "depthSize": "3333",
  "heightSize": "3333",
  "systemId": "34",
  "systemName": "智慧烘烤",
  "baseSubModuleId": "1633",
  "subModuleName": "c",
  "partStatus": 1,
  "roomId": "XB17wdVbiupg-JA8O4Kph",
  "position": [328.62190812720837, 146.28975265017675],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 3999
}, {
  "uuid": "HghWzc6JBYZlineKm8Jjr",
  "type": "Furniture",
  "componentCode": "B508X6000",
  "componentDesc": "LC-197WLC9ED1U1",
  "mainImage": "https://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/B508X6000/productPicture/98764b30352e45368676131b2b5219e4.png",
  "widthSize": null,
  "depthSize": null,
  "heightSize": null,
  "systemId": "15",
  "systemName": "照明",
  "baseSubModuleId": "2075",
  "subModuleName": "大脑屏",
  "partStatus": 0,
  "roomId": "XB17wdVbiupg-JA8O4Kph",
  "position": [175.9717314487632, -76.32508833922259],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 2699.23
}, {
  "uuid": "P-425ZJqPg4sw8ZtL8Nk0",
  "type": "Furniture",
  "componentCode": "B508X8000",
  "componentDesc": "LC-200WLH9EY1",
  "mainImage": "https://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/B508X8000/productPicture/9bc2b6baa0e1474dbc11d393ae1047eb.png",
  "widthSize": null,
  "depthSize": null,
  "heightSize": null,
  "systemId": "15",
  "systemName": "照明",
  "baseSubModuleId": "2075",
  "subModuleName": "大脑屏",
  "partStatus": 0,
  "roomId": "XB17wdVbiupg-JA8O4Kph",
  "position": [409.18727915194336, 36.04240282685521],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 2699.23
}, {
  "uuid": "xdsAWApSMHXo4ZMLoP4E2",
  "type": "Furniture",
  "componentCode": "AB923H001",
  "componentDesc": "CAP7211ZAAH(81)VU1套机",
  "mainImage": "https://haier-ism.oss-cn-qingdao.aliyuncs.com/pre/component/productcenter/AB923H001/productPicture/1e5d0854a2ae4ca792d7d29d27497adf.png",
  "widthSize": null,
  "depthSize": null,
  "heightSize": null,
  "systemId": "16",
  "systemName": "安防",
  "baseSubModuleId": "2092",
  "subModuleName": "直流窗帘电机",
  "partStatus": 0,
  "roomId": "XB17wdVbiupg-JA8O4Kph",
  "position": [201.41342756183735, 59.363957597173226],
  "scale": [1, 1],
  "accessories": [],
  "annotation": {
    "text": null
  },
  "canSetAnnotation": false,
  "pcPrice": 4999
}]
export const selectSceneList = [
  {roomId: 'NeuEYXElEyznc-6vRu3Ib', sceneCode: 'MS000000109'},
  {roomId: 'NeuEYXElEyznc-6vRu3Ib', sceneCode: 'MS000000123'},
  {roomId: 'NeuEYXElEyznc-6vRu3Ib', sceneCode: 'MS000000145'},
  {roomId: 'XB17wdVbiupg-JA8O4Kph', sceneCode: 'MS000000148'},
  {roomId: 'XB17wdVbiupg-JA8O4Kph', sceneCode: 'MS000000155'},
  {roomId: 'XB17wdVbiupg-JA8O4Kph', sceneCode: 'MS000000156'},
  {roomId: 'XB17wdVbiupg-JA8O4Kph', sceneCode: 'MS000000157'},
  {roomId: 'XB17wdVbiupg-JA8O4Kph', sceneCode: 'MS000000145'},
]
