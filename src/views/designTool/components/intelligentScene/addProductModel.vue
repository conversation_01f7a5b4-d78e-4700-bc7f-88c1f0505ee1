<script setup>
  import { _API_getComponentList, _API_getSystemSybModuleAndProduct } from '@/api/sceneApi';
  import ProductSkeleton from '@/components/productSkeleton/productSkeleton.vue';
  import { ref, watch } from 'vue';
  import { MinusOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import ClientImg from '@/components/clientImg/clientImg.vue';
  const emit = defineEmits(['update:modelValue', 'select-product']);
  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    selectedSubModule: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const addProductVisible = ref(false);
  const title = ref('添加商品');
  const searchValue = ref('');
  const categories = ref([]);
  const subModuleCategories = ref([]);
  // 商品数据
  const productList = ref([]);
  const isSkeleton = ref(true);
  const skeletonList = [1, 2, 3, 4, 5];
  const subModuleProductList = ref([]);
  // 选中的商品
  const selectedProduct = ref([]);
  const isSearch = ref(false);
  watch(
    () => props.modelValue,
    async (newValue) => {
      if (newValue) {
        addProductVisible.value = newValue;
        searchValue.value = '';
        isSearch.value = false;
        isSkeleton.value = true;
        // 重置选中状态
        selectedProduct.value = [];
        const params = {
          query: '',
          systemId: '',
          baseSubModuleId: props.selectedSubModule?.subModuleId || '',
        };
        if (props.selectedSubModule.type === 'room') {
          // 左侧边栏点击进入
          title.value = '添加商品';
          await getSystemAndSubModule();
        } else {
          // 子模块点击添加商品进入
          title.value = '添加-' + props.selectedSubModule.subModuleName;
          await getSearchProductList(params, 'all');
        }
      }
    },
    {
      immediate: true,
    }
  );
  function clearSearch() {
    searchValue.value = '';
    isSearch.value = false;
  }
  async function searchProduct() {
    if (!searchValue.value && props.selectedSubModule.type === 'room') {
      isSearch.value = false;
      let subActive = subModuleCategories.value.find((item) => item.active);
      categories.value.forEach((c) => {
        if (c.active) {
          c.subModules.forEach((s) => {
            if (s.subModuleName === subActive?.subModuleName) {
              productList.value = s.components.map((item) => ({
                ...item,
                selected: selectedProduct.value.some((s) => s.componentCode === item.componentCode),
                size:
                  item.widthSize && item.depthSize && item.heightSize
                    ? `${item.widthSize}*${item.depthSize}*${item.heightSize}mm`
                    : '暂无尺寸信息',
              }));
            }
          });
        }
      });
      return;
    }
    const params = {
      query: searchValue.value,
      systemId: '',
      baseSubModuleId: props.selectedSubModule?.subModuleId || '',
    };
    await getSearchProductList(params, 'search');
    isSearch.value = true;
  }
  async function getSearchProductList(params, type) {
    await _API_getComponentList(params).then((res) => {
      isSkeleton.value = false;
      productList.value = res.data.map((item) => ({
        ...item,
        selected: selectedProduct.value.some((s) => s.componentCode === item.componentCode),
        quantity: selectedProduct.value.find((s) => s.componentCode === item.componentCode)?.quantity || 1,
        size:
          item.widthSize && item.depthSize && item.heightSize
            ? `${item.widthSize}*${item.depthSize}*${item.heightSize}mm`
            : '暂无尺寸信息',
      }));
      if (type === 'all') {
        subModuleProductList.value = [...productList.value];
      } else if (type === 'search' && props.selectedSubModule.type === 'subModule') {
        const productMap = new Map(productList.value.map((p) => [p.componentCode, p]));
        subModuleProductList.value.forEach((item) => {
          if (productMap.has(item.componentCode)) {
            productMap.get(item.componentCode).quantity = item.quantity;
          }
        });
        productList.value = [...productMap.values()];
      }
    });
  }
  async function getSystemAndSubModule() {
    await _API_getSystemSybModuleAndProduct().then((res) => {
      isSkeleton.value = false;
      res.data.forEach((item, index) => {
        item.active = index === 0;
        const allComponents = item.subModules.reduce((acc, sub) => acc.concat(sub.components), []);
        item.subModules.unshift({
          components: allComponents,
          subModuleName: '全部',
          baseSubModuleId: '',
          active: true,
        });
      });
      categories.value = res.data;
      if (categories.value.length > 0) {
        changeCategory(categories.value[0]);
      }
    });
  }
  // 切换系统
  function changeCategory(category) {
    categories.value.forEach((item) => {
      item.active = item.systemId === category.systemId;
    });
    subModuleCategories.value = category.subModules.map((item) => ({
      ...item,
      active: item.subModuleName === '全部',
    }));
    if (subModuleCategories.value.length > 0) {
      productList.value = subModuleCategories.value[0].components.map((item) => ({
        ...item,
        selected: selectedProduct.value.some((s) => s.componentCode === item.componentCode),
        size:
          item.widthSize && item.depthSize && item.heightSize
            ? `${item.widthSize}*${item.depthSize}*${item.heightSize}mm`
            : '暂无尺寸信息',
        quantity: item.quantity || 1,
      }));
    }
  }

  // 切换子分类
  function changeSubCategory(subCategory) {
    subModuleCategories.value.forEach((item) => {
      item.active = item.baseSubModuleId === subCategory.baseSubModuleId;
    });
    productList.value = subCategory.components.map((item) => ({
      ...item,
      selected: selectedProduct.value.some((s) => s.componentCode === item.componentCode),
      size:
        item.widthSize && item.depthSize && item.heightSize
          ? `${item.widthSize}*${item.depthSize}*${item.heightSize}mm`
          : '暂无尺寸信息',
      quantity: item.quantity || 1,
    }));
  }

  // 选择商品
  function selectProduct(e, product) {
    if (e.target.checked) {
      selectedProduct.value.push(product);
    } else {
      selectedProduct.value = selectedProduct.value.filter((item) => item.componentCode !== product.componentCode);
    }
  }

  function handleOk() {
    if (selectedProduct.value) {
      emit('select-product', selectedProduct.value);
    }
    addProductVisible.value = false;
    emit('update:modelValue', false);
  }

  function handleUpdateVisible(val) {
    emit('update:modelValue', val);
    addProductVisible.value = false;
  }

  // 增加商品数量
  const increaseQuantity = (product) => {
    product.quantity++;
    subModuleProductList.value = subModuleProductList.value.map((item) =>
      item.componentCode === product.componentCode ? { ...item, quantity: product.quantity } : item
    );
    categories.value.forEach((c) => {
      c.subModules.forEach((s) => {
        const index = s.components.findIndex((item) => item.componentCode === product.componentCode);
        if (index !== -1) {
          s.components[index].quantity = product.quantity;
        }
      });
    });
  };

  // 减少商品数量
  const decreaseQuantity = (product) => {
    if (product.quantity > 1) {
      product.quantity--;
      subModuleProductList.value = subModuleProductList.value.map((item) =>
        item.componentCode === product.componentCode ? { ...item, quantity: product.quantity } : item
      );
      categories.value.forEach((c) => {
        c.subModules.forEach((s) => {
          const index = s.components.findIndex((item) => item.componentCode === product.componentCode);
          if (index !== -1) {
            s.components[index].quantity = product.quantity;
          }
        });
      });
    }
  };
</script>

<template>
  <a-modal
    v-if="addProductVisible"
    v-model:open="addProductVisible"
    :title="title"
    @ok="handleOk"
    @cancel="handleUpdateVisible(false)"
    width="840px">
    <!-- 搜索框 -->
    <div v-if="!isSkeleton" class="search-container">
      <a-input-search
        v-model:value="searchValue"
        placeholder="请输入"
        class="search-input"
        enter-button
        allow-clear
        @clear="clearSearch"
        @search="searchProduct"></a-input-search>
    </div>
    <!-- 系统 -->
    <div v-if="props.selectedSubModule.type === 'room' && !isSearch && !isSkeleton" class="category-container">
      <div
        v-for="category in categories"
        :key="category.systemId"
        class="category-item"
        :class="{ active: category.active }"
        @click="changeCategory(category)">
        {{ category.systemName }}
      </div>
    </div>
    <!-- 子模块 -->
    <div v-if="props.selectedSubModule.type === 'room' && !isSearch && !isSkeleton" class="subcategory-container">
      <div
        v-for="(subCategory, index) in subModuleCategories"
        :key="index"
        class="subcategory-item"
        :class="{ active: subCategory.active }"
        @click="changeSubCategory(subCategory)">
        {{ subCategory.subModuleName }}
      </div>
    </div>
    <div v-if="isSkeleton" class="product-container">
      <div
        v-for="(product, index) in skeletonList"
        :key="product + index"
        class="product-item"
        :class="{ marginRight: (index + 1) % 5 !== 0 }">
        <product-skeleton></product-skeleton>
      </div>
    </div>
    <!-- 商品列表 -->
    <div v-else-if="productList.length > 0 && !isSkeleton" class="product-container">
      <div
        v-for="(product, index) in productList"
        :key="product.componentId"
        class="product-item"
        :class="{ marginRight: (index + 1) % 5 !== 0 }">
        <div class="product-image">
          <a-checkbox
            class="product-select"
            v-model:checked="product.selected"
            @change="selectProduct($event, product)"></a-checkbox>
          <client-img :src="product.mainImage" :preview="false" alt="" />
        </div>
        <div class="product-info">
          <div class="product-name">{{ product.componentDesc }}</div>
          <div class="product-model">{{ product.size }}</div>
          <div class="product-price">
            <div>¥{{ product.pcPrice }}</div>
            <div class="quantity-control">
              <button
                class="quantity-btn minus"
                :class="{ disabled: product.quantity <= 1 }"
                :disabled="product.quantity <= 1"
                @click="decreaseQuantity(product)">
                <MinusOutlined />
              </button>
              <span class="quantity">{{ product.quantity }}</span>
              <button class="quantity-btn plus" @click="increaseQuantity(product)">
                <PlusOutlined />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="product-empty">
      <a-empty />
    </div>
    <template #footer>
      <a-button key="back" @click="handleUpdateVisible(false)">取消</a-button>
      <a-button key="submit" type="primary" @click="handleOk">确认</a-button>
    </template>
  </a-modal>
</template>

<style scoped lang="stylus">
  .search-container
    margin-bottom 15px

    .search-input
      width 100%
      border-radius 4px

  .category-container
    display flex
    margin-bottom 15px

    .category-item
      padding 5px 15px
      margin-right 10px
      cursor pointer
      font-size 14px
      color #333

      &.active
        color #1890ff
        font-weight bold

  .subcategory-container
    display flex
    margin-bottom 10px
    border-radius 4px

    .subcategory-item
      padding 5px 15px
      margin-right 10px
      cursor pointer
      font-size 14px
      color #333
      background #fff
      border-radius 4px

      &.active
        color #1890ff
        font-weight bold

  .product-empty {
    height: 400px;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }

  .quantity-control {
    display: flex;
    align-items: center;
  }

  .quantity-btn {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background-color: #fff;
    cursor: pointer;
    transition: all 0.3s;
    padding: 0;

    &:hover {
      color: #1890ff;
    }

    &.disabled {
      color: #d9d9d9;
      cursor: not-allowed;

      &:hover {
        color: #d9d9d9;
      }
    }
  }

  .quantity {
    width: 24px;
    text-align: center;
    font-size: 14px;
    color: #333;
  }

  .product-container
    display flex
    flex-wrap wrap
    max-height 400px
    width: 100%;
    text-align center
    overflow-y scroll
    .product-item
      width 150px
      box-sizing: border-box
      border 1px solid #e8e8e8
      border-radius 4px
      padding 10px
      cursor pointer
      transition all 0.3s
      margin-bottom 10px

      &.marginRight {
        margin-right 10px;
      }

      .product-image
        width 100%
        position relative
        img {
          width: 100%;
        }
        .product-select {
          position absolute;
          top -8px;
          right -5px;
        }

      .product-info
        text-align left
        .product-name
          font-size 14px
          font-weight bold
          margin-bottom 5px
          color #333
          white-space: nowrap
          overflow: hidden
          text-overflow: ellipsis

        .product-model
          font-size 12px
          color #999
          margin-bottom 5px
          white-space: nowrap
          overflow: hidden
          text-overflow: ellipsis

        .product-price
          font-size 14px
          color #f5222d
          font-weight bold
          display flex
          justify-content space-between
</style>
