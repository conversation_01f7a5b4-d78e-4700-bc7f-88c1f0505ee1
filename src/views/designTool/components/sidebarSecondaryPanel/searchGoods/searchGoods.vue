<script setup>
import { computed } from 'vue'
import DragWrapper from '@/views/designTool/components/dragWrapper/dragWrapper.vue'
import GoodsCard from '@/components/goodsCard/goodsCard.vue'
import Icon from '@/components/icon/icon.vue'
import ProductHoverWrapper from '../ProductHoverWrapper.vue'

// Component definition
defineOptions({
  name: 'SearchGoods'
})

// Props
const props = defineProps({
  goodsList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  searching: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'item-click',
  'item-drag-start',
  'item-drag-end',
  'item-drop'
])

// Computed
const isEmpty = computed(() => {
  return !props.loading && !props.searching && (!props.goodsList || props.goodsList.length === 0)
})



/**
 * @description 处理拖拽开始事件
 */
const handleDragStart = (event) => {
  emit('item-drag-start', event)
}

/**
 * @description 处理拖拽结束事件
 */
const handleDragEnd = (event) => {
  emit('item-drag-end', event)
}

/**
 * @description 处理放置事件
 */
const handleDrop = (event) => {
  emit('item-drop', event)
}

/**
 * @description 处理商品点击事件
 */
const handleItemClick = (item) => {
  emit('item-click', item)
}
</script>

<template>
  <div class="search-goods-container">
    <div class="search-goods-content">
      <!-- 商品列表 - 一行两列布局 -->
      <div v-if="!loading && !searching && goodsList.length > 0" class="goods-grid">
        <DragWrapper
          v-for="(item, index) in goodsList"
          :key="item.id || index"
          :drag-data="item"
          :drag-type="'search-item'"
          class="grid-item-wrapper"
          @drag-start="handleDragStart"
          @drag-end="handleDragEnd"
          @drop="handleDrop"
        >
          <div class="grid-item" data-draggable @click="handleItemClick(item)">
            <ProductHoverWrapper :product-data="item">
              <GoodsCard :stop-propagation="false" :border="false" :goods-obj="item" />
            </ProductHoverWrapper>
          </div>
        </DragWrapper>
      </div>

      <!-- 骨架屏状态 -->
      <div v-if="loading || searching" class="loading-state">
        <div class="skeleton-grid">
          <div
            v-for="n in 6"
            :key="`skeleton-${n}`"
            class="skeleton-item-wrapper"
          >
            <div class="skeleton-item">
              <a-skeleton
                active
                :paragraph="{ rows: 3, width: ['100%', '80%', '60%'] }"
                :title="false"
                :avatar="{ size: 'large', shape: 'square' }"
              >
                <template #avatar>
                  <div class="skeleton-image"></div>
                </template>
              </a-skeleton>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="isEmpty" class="empty-state">
        <div class="empty-content">
          <Icon icon="synEmpty" class="empty-icon" />
          <p class="empty-text">暂无搜索结果</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.search-goods-container {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

.search-goods-content {
  height: 100%;

}

/* 2-column flex layout for better browser compatibility */
.goods-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  box-sizing: border-box;
  padding: 0;
}

.grid-item-wrapper {
  /* 计算每个项目的宽度：(100% - gap) / 2 */
  width: calc(50% - 4px);
  flex-shrink: 0;
  flex-grow: 0;
  box-sizing: border-box;
  min-width: 0; /* 防止内容溢出 */
}

/* 解决奇数项目时最后一个空白的问题 */
.goods-grid .grid-item-wrapper:last-child:nth-child(odd) {
  width: 100%; /* 如果是最后一个且为奇数位置，占满整行 */
}

/* 更强的规则：确保任何奇数位置的最后一个元素都能正确处理 */
.goods-grid .grid-item-wrapper:nth-last-child(1):nth-child(odd) {
  width: 100% !important;
}

/* JavaScript控制的最后奇数项目样式 */
.grid-item-wrapper.last-odd-item {
  width: 100% !important;
}

.grid-item {
  width: 100%;
  height: auto; /* 改为auto，让内容决定高度 */
  min-height: 140px; /* 设置最小高度确保卡片完整显示 */
  display: flex;
  flex-direction: column;
  background-color: #f8f7f8;
  border-radius: 10px;
  transition: all 0.2s ease;
  overflow: hidden;
  box-sizing: border-box;
  cursor: pointer;
  /* 确保卡片内容不会被截取 */
  word-wrap: break-word;
  word-break: break-word;
}

.grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Fine-tune GoodsCard styling to match Figma design */
.grid-item :deep(.goods-wrap) {
  background: #f8f7f8;
  border-radius: 10px;
  padding: 8px;
  height: 100%;
  min-height: 140px; /* 确保卡片有足够的高度 */
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* 内容分布均匀 */
}

.grid-item :deep(.top-img) {
  height: 88px;
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 4px;
  flex-shrink: 0; /* 防止图片被压缩 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.grid-item :deep(.top-img img) {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 确保图片完整显示 */
  object-position: center;
}

.grid-item :deep(.bottom-into) {
  background: #F5F5F5;
  border-radius: 0 0 8px 8px;
  padding: 4px 0;
  text-align: center;
  gap: 2px;
  flex-grow: 1; /* 占据剩余空间 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 48px; /* 确保文本区域有足够高度 */
}

.grid-item :deep(.name) {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  margin: 0;
  /* 处理文本溢出 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.grid-item :deep(.size) {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 10px;
  line-height: 14px;
  color: rgba(0, 0, 0, 0.45);
  text-align: center;
  margin: 0;
  /* 处理文本溢出 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

/* Price styling to match Figma */
.grid-item :deep(.price-wrap) {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 16.8px;
  color: #1a1a1a;
  text-align: center;
  margin: 0;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 40px 20px;
}

.loading-state {
  padding: 0;
  min-height: auto;
}

/* 骨架屏样式 - 保持与商品卡片相同的布局 */
.skeleton-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  width: 100%;
  box-sizing: border-box;
}

.skeleton-item-wrapper {
  width: calc(50% - 4px);
  flex-shrink: 0;
  flex-grow: 0;
  box-sizing: border-box;
  min-width: 0;
}

/* 骨架屏最后一个奇数项目占满整行 */
.skeleton-item-wrapper:last-child:nth-child(odd) {
  width: 100%;
}

.skeleton-item {
  width: 100%;
  min-height: 140px;
  background-color: #f8f7f8;
  border-radius: 10px;
  padding: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.skeleton-image {
  width: 100%;
  height: 88px;
  background-color: #f0f0f0;
  border-radius: 6px;
  margin-bottom: 4px;
  animation: skeleton-loading 1.5s ease-in-out infinite alternate;
}

/* 骨架屏动画 */
@keyframes skeleton-loading {
  0% {
    background-color: #f0f0f0;
  }
  100% {
    background-color: #e0e0e0;
  }
}

/* 自定义骨架屏内容样式 */
.skeleton-item :deep(.ant-skeleton) {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.skeleton-item :deep(.ant-skeleton-content) {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 4px 0;
}

.skeleton-item :deep(.ant-skeleton-paragraph) {
  margin-bottom: 0;
}

.skeleton-item :deep(.ant-skeleton-paragraph li) {
  height: 12px;
  margin-bottom: 4px;
  border-radius: 4px;
}

.skeleton-item :deep(.ant-skeleton-paragraph li:last-child) {
  margin-bottom: 0;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  opacity: 0.4;
  color: #ccc;
}

.empty-text {
  font-size: 14px;
  color: #999;
  margin: 0;
  line-height: 1.4;
}

/* Cross-browser compatibility for Flex layout */
.goods-grid {
  /* Fallback for older browsers */
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;

  /* Flex wrap support */
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.grid-item-wrapper {
  /* Flex basis for IE10+ */
  -webkit-flex-basis: calc(50% - 4px);
  -ms-flex-preferred-size: calc(50% - 4px);
  flex-basis: calc(50% - 4px);

  /* Prevent flex grow/shrink */
  -webkit-flex-grow: 0;
  -ms-flex-positive: 0;
  flex-grow: 0;

  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

/* Ensure consistent box-sizing */
.goods-grid,
.grid-item-wrapper,
.grid-item,
.grid-item *,
.grid-item *::before,
.grid-item *::after {
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

/* Remove GPU acceleration transforms to avoid DragWrapper conflicts */
.grid-item:hover {
  transform: translateY(-2px);
  -webkit-transform: translateY(-2px);
  -moz-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
}

/* Custom scrollbar */
.search-goods-content::-webkit-scrollbar {
  width: 4px;
}

.search-goods-content::-webkit-scrollbar-track {
  background: transparent;
}

.search-goods-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.search-goods-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.2);
}
</style>
