<script setup>
  import { computed, ref } from 'vue';
  import Icon from '@/components/icon/icon.vue';
  import DragWrapper from '@/views/designTool/components/dragWrapper/dragWrapper.vue';
  import GoodsCard from '@/components/goodsCard/goodsCard.vue';

  const props = defineProps({
    itemData: {
      type: Object,
      default: () => {
        return {
          title: '',
          list: [],
        };
      },
    },
  });

  const emit = defineEmits(['item-drag-start', 'item-drag-end', 'item-drop']);

  const toggle = ref(true);
  const expandIcon = computed(() => {
    return toggle.value ? 'synArrowBottom' : 'synArrowTop';
  });

  /**
   * @description 切换展开状态
   */
  function toggleExpand() {
    toggle.value = !toggle.value;
  }

  /**
   * @description 处理拖拽开始事件
   */
  function handleDragStart(event) {
    emit('item-drag-start', event);
  }

  /**
   * @description 处理拖拽结束事件
   */
  function handleDragEnd(event) {
    emit('item-drag-end', event);
  }

  /**
   * @description 处理放置事件
   */
  function handleDrop(event) {
    emit('item-drop', event);
  }
</script>
<template>
  <div class="toggle-expand-container">
    <div class="toggle-expand-header" @click="toggleExpand">
      <span class="toggle-expand-title">{{ props.itemData.title }}</span>
      <Icon :icon="expandIcon"></Icon>
    </div>
    <div class="toggle-expand-content" :style="{ height: toggle ? 'auto' : '0' }">
      <slot name="default" :item="props.itemData">
        <DragWrapper
          v-for="(item, index) in props.itemData.list"
          :key="index"
          :drag-data="item"
          :drag-type="'toggle-item'"
          class="toggle-item-wrapper"
          @drag-start="handleDragStart"
          @drag-end="handleDragEnd"
          @drop="handleDrop">
          <div class="toggle-item" data-draggable>
            <goods-card :goods-obj="item" :stopPropagation="false"></goods-card>
          </div>
        </DragWrapper>
      </slot>
    </div>
  </div>
</template>

<style scoped lang="stylus">
  .toggle-expand-container {
    display flex;
    height auto;
    flex-direction column;
  }
  .toggle-expand-header {
    cursor pointer;
    @extends .flex-align-center;
    @extends .flex-spb;
  }
  .toggle-expand-content{
    display flex;
    height auto
    flex-direction row;
    flex-wrap wrap;
    overflow hidden;
    width 100%;
    gap 10px;
    will-change height;
    transition height .32s ease-in-out
    interpolate-size: allow-keywords
  }
  .toggle-item-wrapper {
    flex-shrink: 0;
  }
  .toggle-item {
    padding: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: var(--card-hover-gb-color);
    border-radius: 8px;
    transition: all 0.2s ease;
    .toggle-item-image {
      width: 84px;
      height 84px;
      object-fit contain;
    }
    &:hover {
      background-color: #e8e8e8;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }
  .toggle-item-name {
    @extends .content-small;
  }

  // 自定义拖拽图像样式
  .custom-drag-image {
    display: flex;
    align-items: center;
    gap: 8px;
    background #f00;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(4px);
  }

  .drag-image-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    object-fit: cover;
  }

  .drag-image-text {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    white-space: nowrap;
  }
</style>
