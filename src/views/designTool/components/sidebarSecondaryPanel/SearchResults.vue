<template>
  <Transition name="search-fade" mode="out-in">
    <div v-if="!hidden" class="search-results">
      <slot name="default" :results="results" :loading="loading" :empty="isEmpty">
        <!-- Default search results using SearchGoods component -->
        <SearchGoods
          :goods-list="results"
          :loading="loading"
          :searching="loading"
          @item-click="handleItemClick"
          @item-drag-start="handleItemDragStart"
          @item-drag-end="handleItemDragEnd"
          @item-drop="handleItemDrop"
        />
      </slot>
    </div>
  </Transition>
</template>

<script setup>
import { computed, defineProps, defineEmits } from 'vue'
import SearchGoods from '@/views/designTool/components/sidebarSecondaryPanel/searchGoods/searchGoods.vue'

// Component definition
defineOptions({
  name: 'SearchResults'
})

// Props
const props = defineProps({
  results: {
    type: Array,
    default: () => []
  },
  searchQuery: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  hidden: {
    type: Boolean,
    default: false
  },
  showHeader: {
    type: Boolean,
    default: true
  },
  showClearButton: {
    type: Boolean,
    default: true
  },
  totalCount: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits([
  'clear',
  'item-click',
  'item-drag-start',
  'item-drag-end',
  'item-drop'
])

// Computed
const isEmpty = computed(() => {
  return !props.loading && (!props.results || props.results.length === 0)
})

// Methods
const handleItemClick = (item) => {
  emit('item-click', item)
}

const handleItemDragStart = (event) => {
  emit('item-drag-start', event)
}

const handleItemDragEnd = (event) => {
  emit('item-drag-end', event)
}

const handleItemDrop = (event) => {
  emit('item-drop', event)
}
</script>

<style scoped>
.search-results-container {
  /* Remove internal height constraints - parent handles scrolling */
  width: 100%;
  background: #fff;

  /* Remove transform to avoid DragWrapper conflicts */
}

.search-results {
  /* Remove internal height constraints */
  width: 100%;
  padding: 12px 0;
}

.search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid var(--opn-border-color, #ececec);
  background: #fafbfc;
  flex-shrink: 0;
}

.search-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.search-query {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.search-query::before {
  content: '"';
  color: var(--opn-color-primary, #be965a);
}

.search-query::after {
  content: '"';
  color: var(--opn-color-primary, #be965a);
}

.search-count {
  font-size: 12px;
  color: #999;
  line-height: 1.2;
}

.clear-search-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: 1px solid var(--opn-border-color, #ececec);
  background: #fff;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  transition: all 0.2s ease;
}

.clear-search-btn:hover {
  border-color: var(--opn--hover-color, #CCA054);
  color: var(--opn-color-primary, #be965a);
  background: var(--card-hover-gb-color, #f5f5f5);
}

.search-content {
  /* Remove internal scrolling - parent handles it */
  width: 100%;
  padding: 16px 0;
}

/* Transitions */
.search-fade-enter-active,
.search-fade-leave-active {
  transition: all 0.3s ease;
}

.search-fade-enter-from {
  opacity: 0;
  transform: translateY(10px);
}

.search-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Responsive design */
@media (max-width: 768px) {
  .search-header {
    padding: 8px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .clear-search-btn {
    align-self: flex-end;
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .search-content {
    padding: 12px 0;
  }
}
</style>
