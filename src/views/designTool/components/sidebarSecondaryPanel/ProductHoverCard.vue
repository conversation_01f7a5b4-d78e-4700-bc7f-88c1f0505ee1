<template>
  <div class="product-hover-card">
    <!-- Product Image -->
    <div class="product-image">
      <client-img
        :src="productData.mainImage"
        :preview="false"
        fit="contain"
        class="hover-card-image" />
    </div>

    <!-- Product Information -->
    <div class="product-info">
      <!-- Product Name and Category -->
      <div class="product-header">
        <div class="product-name">
          {{ productData.componentDesc }}
        </div>
        <div v-if="productData.subModuleName" class="product-category">
          {{ productData.subModuleName }}
        </div>
      </div>

      <!-- Product Description -->
      <div class="product-description">
        <div class="description-line">
          {{ productData.zkSellingPoint || "--" }}
        </div>
      </div>

      <!-- Product Price -->
      <div class="product-price">
        <Price
          :price="productData.pcPrice"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'
import ClientImg from '@/components/clientImg/clientImg.vue'
import Price from '@/components/price/price.vue'

// Component definition
defineOptions({
  name: 'ProductHoverCard'
})

// Props
defineProps({
  productData: {
    type: Object,
    required: true,
    default: () => ({})
  }
})
</script>

<style scoped>
/* 根据Figma设计的精确尺寸和样式 */
.product-hover-card {
  width: 300px;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 3px 6px -4px rgba(0, 0, 0, 0.12), 0px 6px 16px 0px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 0px 0px 8px;
}

.product-image {
  width: 300px;
  height: 300px;
  background: #F8F7F8;
  border-radius: 10px 10px 0px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.hover-card-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background-size: contain;
  background-repeat: no-repeat;
}

.product-info {
  width: 300px;
  border-radius: 0px 0px 8px 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 4px;
  padding: 0px 12px;
}

.product-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 6px;
}

.product-name {
  flex: 1;
  text-overflow: ellipsis;
  font-family: 'PingFang SC';
  font-weight: 500;
  font-size: 14px;
  line-height: 1.5714285714285714em;
  color: rgba(0, 0, 0, 0.85);
}

.product-category {
  flex-shrink: 0;
  background: #FFFBF0;
  border-radius: 6px;
  padding: 0px 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 3px;
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 12px;
  line-height: 1.6666666666666667em;
  color: #BF8630;
}

.product-description {
  display: flex;
  flex-direction: column;
}

.description-line {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 12px;
  line-height: 1.6666666666666667em;
  color: rgba(0, 0, 0, 0.45);
}

.product-price {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 12px;
  line-height: 1.3999999364217122em;
  text-align: center;
  color: #1A1A1A;
}


</style>
