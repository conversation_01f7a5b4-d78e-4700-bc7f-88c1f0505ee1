<script setup>
  import { computed, ref } from 'vue';
  import Uploader from '@/components/upload/uploader.vue';
  import ClientImg from '@/components/clientImg/clientImg.vue';
  import { RECOGNISE_DIALOG_STATE, useGlobalModalStore } from '@/store/modules/globalModalStore';
  import { _API_recogniseHouse } from '@/api/recogniseHouse';
  import { message } from '@syn/ant-design4-vue3';
  import { isFunction } from '@/utils/isType';
  const globalModalStore = useGlobalModalStore();
  const recogniseStore = globalModalStore[RECOGNISE_DIALOG_STATE];

  const emit = defineEmits(['recogniseHouse']);
  const open = ref(true);
  const confirmLoading = ref(false);
  const imgUrl = ref(null);

  const hasImg = computed(() => {
    return imgUrl.value !== null;
  });
  // 户型识别确认事件
  async function handleConfirm() {
    if (!imgUrl.value) {
      message.warn('请先选择户型图');
      return;
    }

    confirmLoading.value = true;
    _API_recogniseHouse(imgUrl.value)
      .then((res) => {
        if (res.code === '0') {
          emit('onAction', {
            action: 'recogniseHouse',
            payload: {
              image: imgUrl.value,
              data: res.data,
            },
          });
          handleCancel();
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  // 取消事件
  function handleCancel() {
    // 调用对应modal的Event
    if (isFunction(recogniseStore.event)) {
      recogniseStore.event();
    }
    globalModalStore.clearStoreState(RECOGNISE_DIALOG_STATE);
  }

  /**
   * @description 文件上传状态发生变化
   * @param files
   */
  function handleFileChange({ files }) {
    console.log(files[0]);
    if (files.length) {
      let file = files[0];
      if (file.status === 'done') {
        imgUrl.value = file?.response?.url;
      }
    }
  }
</script>

<template>
  <a-modal
    v-model:open="open"
    title="户型识别"
    @ok="handleConfirm"
    :maskClosable="false"
    :keyboard="false"
    width="40%"
    @cancel="handleCancel"
    :confirmLoading="confirmLoading">
    <div class="recognise-container">
      <template v-if="!hasImg">
        <uploader accept=".jpg,.png,.webp" width="100%" height="300px" @onChange="handleFileChange"></uploader>
      </template>
      <template v-else>
        <client-img :src="imgUrl" :ignoreCompress="true"></client-img>
      </template>
    </div>
  </a-modal>
</template>

<style scoped lang="stylus">
  .recognise-container
    position: relative;
    min-height: 300px;
</style>
