import { checkboxGroupFormItem, initFormConfig, radioGroupGroupFormItem } from '@/utils/createForm';
import { useDictStore } from '@/store/modules/dictStore';
import { SYSTEM_LIST } from '@/config/dictKeys';

export async function workingFormConfig() {
  const dictStore = useDictStore();
  return {
    imgType: {
      is: 'a-checkbox-group',
      label: '生成范围',
      event: ['change'],
      ...checkboxGroupFormItem({
        options: await dictStore.getDictStore(SYSTEM_LIST),
        placeholder: '请选择生成范围',
        rules: ['changeRequired'],
      }),
    },
    renderSize: {
      is: 'a-radio-group',
      label: '生成尺寸',
      event: ['change'],
      ...radioGroupGroupFormItem({
        placeholder: '请选择生成尺寸',
        options: [
          {
            label: '1080p',
            value: 1,
          },
          {
            label: '2K',
            value: 2,
          },
          {
            label: '4K',
            value: 3,
          },
        ],
        rules: ['changeRequired'],
      }),
    },
  };
}
/**
 * @description: 生成施工生成配置
 */
export async function createWorkingForm() {
  let config = await workingFormConfig();
  let { formData, formItems } = initFormConfig(config);
  return { formData, formItems };
}
