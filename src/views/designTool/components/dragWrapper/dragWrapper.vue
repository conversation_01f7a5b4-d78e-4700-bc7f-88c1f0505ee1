<template>
  <div
    ref="dragContainer"
    class="drag-wrapper"
    @mousedown="handleMouseDown"
    @touchstart="handleTouchStart"
    @click="handleClick"
    @contextmenu="handleRightClick"
    @dragstart.prevent>
    <slot></slot>

    <!-- 拖拽图像 -->
    <div v-if="isDragging" ref="dragImage" class="drag-image" :style="dragImageStyle">
      <img v-if="dragData?.img" :src="dragData.img" :alt="dragData?.name || ''" class="drag-image-only" />
      <div v-else class="default-drag-image">拖</div>
    </div>
  </div>
</template>

<script setup>
  //  拖拽实体构建
  import { ref, computed, onUnmounted, onMounted } from 'vue';

  const props = defineProps({
    // 是否启用拖拽
    draggable: {
      type: Boolean,
      default: true,
    },
    // 拖拽数据
    dragData: {
      type: [Object, String, Number],
      default: null,
    },
    // 拖拽类型
    dragType: {
      type: String,
      default: 'default',
    },
    // 是否允许放置
    allowDrop: {
      type: Boolean,
      default: true,
    },
    // 拖拽区域限制
    dragBounds: {
      type: Object,
      default: () => ({
        top: 50,
        right: 240,
        left: 300,
        bottom: null, // null表示不限制
      }),
    },
  });

  const emit = defineEmits([
    'drag-start',
    'drag-move',
    'drag-end',
    'drag-enter',
    'drag-leave',
    'drag-over',
    'drop',
    'click-drag',
    'drag-cancel',
  ]);

  const dragContainer = ref(null);
  const dragImage = ref(null);
  const isDragging = ref(false);
  const dragStartPos = ref({ x: 0, y: 0 });
  const currentPos = ref({ x: 0, y: 0 });
  const isMouseDown = ref(false);
  const dragThreshold = 3; // 拖拽阈值

  // 限制拖拽位置在边界内
  const constrainPosition = (x, y) => {
    const bounds = props.dragBounds;
    let constrainedX = x;
    let constrainedY = y;

    // 限制左边界
    if (bounds.left !== null && x < bounds.left) {
      constrainedX = bounds.left;
    }

    // 限制右边界（从窗口右侧计算）
    if (bounds.right !== null && x > window.innerWidth - bounds.right) {
      constrainedX = window.innerWidth - bounds.right;
    }

    // 限制上边界
    if (bounds.top !== null && y < bounds.top) {
      constrainedY = bounds.top;
    }

    // 限制下边界（从窗口底部计算）
    if (bounds.bottom !== null && y > window.innerHeight - bounds.bottom) {
      constrainedY = window.innerHeight - bounds.bottom;
    }

    return { x: constrainedX, y: constrainedY };
  };

  // 拖拽图像样式
  const dragImageStyle = computed(() => ({
    position: 'fixed',
    left: `${currentPos.value.x}px`,
    top: `${currentPos.value.y}px`,
    zIndex: 9999,
    pointerEvents: 'none',
    transform: 'translate(-50%, -50%)',
    opacity: isDragging.value ? 1 : 0,
  }));

  // 禁用默认拖拽
  const disableDefaultDrag = () => {
    if (dragContainer.value) {
      const draggableElements = dragContainer.value.querySelectorAll('[data-draggable]');
      draggableElements.forEach((el) => {
        el.draggable = false;
        el.addEventListener('dragstart', (e) => {
          e.preventDefault();
          e.stopPropagation();
        });
        el.addEventListener('selectstart', (e) => {
          e.preventDefault();
          e.stopPropagation();
        });
      });
    }
  };

  // 处理点击事件构建拖拽实体
  const handleClick = (event) => {
    if (!props.draggable) return;

    const target = event.target.closest('[data-draggable]');
    if (!target) return;

    // 防止与mousedown事件冲突
    if (isMouseDown.value) return;

    // 设置拖拽起始位置
    dragStartPos.value = {
      x: event.clientX,
      y: event.clientY,
    };
    currentPos.value = { ...dragStartPos.value };

    // 立即启动拖拽状态
    startDrag(event);

    // 绑定全局鼠标移动和释放事件
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // 创建拖拽实体
    const dragEntity = {
      type: props.dragType,
      data: props.dragData,
      startPos: dragStartPos.value,
      timestamp: Date.now(),
    };

    emit('click-drag', {
      event,
      dragEntity,
      position: currentPos.value,
    });
  };

  // 鼠标按下事件
  const handleMouseDown = (event) => {
    if (!props.draggable) return;

    const target = event.target.closest('[data-draggable]');
    if (!target) return;

    isMouseDown.value = true;
    dragStartPos.value = {
      x: event.clientX,
      y: event.clientY,
    };
    currentPos.value = { ...dragStartPos.value };

    // 绑定全局事件
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // 触摸开始事件
  const handleTouchStart = (event) => {
    if (!props.draggable) return;

    const target = event.target.closest('[data-draggable]');
    if (!target) return;

    dragStartPos.value = {
      x: event.touches[0].clientX,
      y: event.touches[0].clientY,
    };
    currentPos.value = { ...dragStartPos.value };

    // 绑定全局事件
    document.addEventListener('touchmove', handleTouchMove, { passive: false });
    document.addEventListener('touchend', handleTouchEnd);
  };

  // 开始拖拽
  const startDrag = (event) => {
    isDragging.value = true;

    // 直接禁用main-canvas画布点击
    const canvas = document.getElementById('main-canvas');
    if (canvas) {
      canvas.style.pointerEvents = 'none';
    }

    // 创建拖拽实体
    const dragEntity = {
      type: props.dragType,
      data: props.dragData,
      startPos: dragStartPos.value,
      timestamp: Date.now(),
    };

    // 添加拖拽样式
    document.body.style.cursor = 'grabbing';
    document.body.style.userSelect = 'none';

    emit('drag-start', {
      event,
      dragEntity,
      position: currentPos.value,
    });
  };

  // 鼠标移动事件
  const handleMouseMove = (event) => {
    // 如果是通过mousedown启动的拖拽，需要检查isMouseDown状态
    if (isMouseDown.value) {
      const deltaX = Math.abs(event.clientX - dragStartPos.value.x);
      const deltaY = Math.abs(event.clientY - dragStartPos.value.y);

      // 检查是否达到拖拽阈值
      if (!isDragging.value && (deltaX > dragThreshold || deltaY > dragThreshold)) {
        startDrag(event);
      }
    }

    // 如果没有在拖拽状态，直接返回
    if (!isDragging.value) return;

    // 应用边界限制
    const constrainedPos = constrainPosition(event.clientX, event.clientY);
    currentPos.value = constrainedPos;

    emit('drag-move', {
      event,
      position: currentPos.value,
      delta: {
        x: currentPos.value.x - dragStartPos.value.x,
        y: currentPos.value.y - dragStartPos.value.y,
      },
    });
  };

  // 触摸移动事件
  const handleTouchMove = (event) => {
    event.preventDefault();

    const touch = event.touches[0];
    const deltaX = Math.abs(touch.clientX - dragStartPos.value.x);
    const deltaY = Math.abs(touch.clientY - dragStartPos.value.y);

    // 检查是否达到拖拽阈值
    if (!isDragging.value && (deltaX > dragThreshold || deltaY > dragThreshold)) {
      startDrag(event);
    }

    if (!isDragging.value) return;

    // 应用边界限制
    const constrainedPos = constrainPosition(touch.clientX, touch.clientY);
    currentPos.value = constrainedPos;

    emit('drag-move', {
      event,
      position: currentPos.value,
      delta: {
        x: currentPos.value.x - dragStartPos.value.x,
        y: currentPos.value.y - dragStartPos.value.y,
      },
    });
  };

  // 鼠标释放事件
  const handleMouseUp = (event) => {
    // 重置mousedown状态
    if (isMouseDown.value) {
      isMouseDown.value = false;
    }

    // 结束拖拽
    endDrag(event);
  };

  // 触摸结束事件
  const handleTouchEnd = (event) => {
    endDrag(event);
  };

  // ESC键监听函数
  const handleKeyDown = (event) => {
    if (event.key === 'Escape' && isDragging.value) {
      cancelDrag();
    }
  };

  // 鼠标右键监听函数
  const handleRightClick = (event) => {
    // 如果正在拖拽，则取消拖拽并阻止右键菜单
    if (isDragging.value) {
      event.preventDefault();
      event.stopPropagation();
      cancelDrag();
    }
  };

  // 全局右键监听函数
  const handleGlobalRightClick = (event) => {
    // 如果正在拖拽，则取消拖拽
    if (isDragging.value) {
      event.preventDefault();
      cancelDrag();
    }
  };

  // 取消拖拽
  const cancelDrag = () => {
    if (!isDragging.value) return;

    isDragging.value = false;
    isMouseDown.value = false;

    // 移除全局事件
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);

    // 恢复样式
    document.body.style.cursor = '';
    document.body.style.userSelect = '';

    // 重新启用main-canvas画布点击
    const canvas = document.getElementById('main-canvas');
    if (canvas) {
      canvas.style.pointerEvents = 'auto';
    }

    // 触发取消事件
    emit('drag-cancel', {
      position: currentPos.value,
    });
  };

  // 结束拖拽
  const endDrag = (event) => {
    event.preventDefault();

    if (!isDragging.value) return;

    // 阻止事件传播，防止触发画布上的点击事件
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // 显示事件阻止遮罩，防止点击事件穿透
    isDragging.value = false;

    // 移除全局事件
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);

    // 恢复样式
    document.body.style.cursor = '';
    document.body.style.userSelect = '';

    const dragEntity = {
      type: props.dragType,
      data: props.dragData,
      startPos: dragStartPos.value,
      endPos: currentPos.value,
      timestamp: Date.now(),
    };

    emit('drag-end', {
      event,
      dragEntity,
      position: currentPos.value,
    });
    const canvas = document.getElementById('main-canvas');
    if (canvas) {
      canvas.style.pointerEvents = 'auto';
    }
  };

  // 组件挂载时禁用默认拖拽
  onMounted(() => {
    disableDefaultDrag();
    // 添加ESC键监听
    document.addEventListener('keydown', handleKeyDown);
    // 添加全局右键监听
    document.addEventListener('contextmenu', handleGlobalRightClick);
  });

  // 组件卸载时清理事件
  onUnmounted(() => {
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    document.removeEventListener('touchmove', handleTouchMove);
    document.removeEventListener('touchend', handleTouchEnd);
    document.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('contextmenu', handleGlobalRightClick);
  });
</script>

<style scoped lang="stylus">
  .drag-wrapper {
    position: relative;
  }

  .drag-image {
    width: 80px;
    height: 80px;
    flex-shrink: 0;
    aspect-ratio: 1/1;
    border: 2px solid #1890FF;
    background transparent;
    transition: opacity 0.2s ease;
  }
  .drag-image-only {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .default-drag-image {
    font-size: 14px;
    color: #333;
    white-space: nowrap;
  }

  // 可拖拽元素的样式
  [data-draggable] {
    cursor: grab;
    user-select: none;

    &:active {
      cursor: grabbing;
    }
  }
</style>
