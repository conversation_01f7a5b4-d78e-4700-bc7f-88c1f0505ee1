<template>
  <a-modal
    v-model:open="open"
    :title="props.type === 'serviceFee' ? '服务费设置' : '折扣设置'"
    :footer="null"
    width="400px"
    wrapClassName="fee-setting-modal"
    @cancel="handleCancel">
    <div class="fee-setting-content">
      <div class="input-group">
        <span class="label">{{ props.type === 'serviceFee' ? '服务费比例:' : '折扣比例:' }}</span>
        <a-input-number v-model:value="discountRate" placeholder="请输入比例" class="input">
          <template #addonAfter>%</template>
        </a-input-number>
      </div>

      <div class="button-group">
        <a-button @click="handleCancel" class="cancel-btn">取消</a-button>
        <a-button type="primary" @click="handleConfirm" class="confirm-btn">确认</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, defineProps, defineEmits, watch } from 'vue';
  import { _API_saveServiceFeeRate, _API_saveProductDiscountRate } from '@/api/userInfo.js';
  import { message } from 'ant-design-vue';
  import { useUserStore } from '@/store/modules/userStore';

  const userStore = useUserStore();
  const props = defineProps({
    rate: {
      type: Number,
      required: true,
    },
    type: {
      type: String,
      required: true,
      validator: (value) => ['discount', 'serviceFee'].includes(value),
    },
  });
  const open = ref(true);
  const emit = defineEmits(['cancel']);
  const discountRate = ref(props.rate);
  // const designerId = userStore?.zjsjUserInfo?.user?.id;

  // 从父组件接收初始利率值
  discountRate.value = props.rate || 0;

  // 监听父组件 rate 变化，更新 discountRate
  watch(
    () => props.rate,
    (newRate) => {
      discountRate.value = newRate || 0;
    }
  );

  const handleCancel = () => {
    emit('cancel');
  };

  const handleConfirm = async () => {
    try {
      if (discountRate.value === props.rate) {
        emit('cancel');
        return;
      }
      const saveParams =
        props.type === 'serviceFee'
          ? { zkServiceFeeRate: discountRate.value }
          : { productDiscountRate: discountRate.value, code: userStore.saveCaseData?.code };
      const res = await (props.type === 'serviceFee' ? _API_saveServiceFeeRate : _API_saveProductDiscountRate)(
        saveParams
      );
      if (res?.code === '0') {
        message.success('设置成功');
        emit('success');
      }
    } catch (error) {
      message.error('设置失败，请稍后重试');
      console.error('Failed to save service fee rate:', error);
    }
  };
</script>

<style scoped lang="stylus">
  .fee-setting-content
    background #ffffff

  .description
    color #4e5969
    margin-bottom 28px
    font-size 14px
    line-height 22px
    padding-left 2px

  .input-group
    display flex
    align-items center
    padding 20px 0
  .label
    margin-right 12px
    text-align right
    font-size 14px
    color rgba(0, 0, 0, 0.85)
    font-family "PingFang SC"
    font-style normal
    font-weight 400

  .input
    width 180px

  .button-group
    display flex
    justify-content flex-end
    gap 16px

  .cancel-btn
    height 32px
    border-radius 6px
    font-size 14px
    font-weight 500
    padding 0 16px
    border-radius: 8px

  .confirm-btn
    height 32px
    border-radius 6px
    font-size 14px
    font-weight 500
    padding 0 16px
    border-radius: 8px
</style>
<style lang="stylus">
  .fee-setting-modal
    .ant-modal-content
      padding 20px
    .ant-modal-header
      margin 0
    .ant-input-number-wrapper
      border-radius: 8px
    .ant-input-number-group-addon
      background: transparent
      color rgba(0, 0, 0, 0.25)
      font-family "PingFang SC"
      font-size 14px
      font-style normal
      font-weight 400
      line-height 22px
    .ant-input-number
      border-right: none;
</style>
