import setPageTitle from '@/utils/set-page-title';
const url = new URL(window.location.href);
const urlParams = new URLSearchParams(url.search);
// 获取链接上的name字段
const nameFromUrl = urlParams.get('name');

function setupGuard(router) {
  // 全局前置守卫
  router.beforeEach((to, from, next) => {
    // 页面 title
    setPageTitle(nameFromUrl || to.meta.title);
    next();
  });

  // 全局后置守卫
  router.afterEach(() => {});
}

export default setupGuard;
