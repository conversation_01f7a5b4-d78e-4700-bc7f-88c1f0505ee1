import baseLayout from '@/layouts/baseLayout.vue';
import { isDev } from '@/utils/isEnv';
import { createRouter, createWebHashHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    name: 'root',
    component: baseLayout,
    children: [
      {
        path: '/',
        name: 'designTool',
        component: () => import('@/views/designTool/designTool.vue'),
        meta: {
          title: '智控设计工具',
        },
      },
      {
        path: '/:pathMatch(.*)',
        meta: {
          title: '找不到页面',
        },
        component: () => import('@/views/result/404.vue'),
      },
    ],
  },
];

const routerBase = isDev() ? '/' : '/biz';
// 注册路由
const router = createRouter({
  history: createWebHashHistory(routerBase),
  routes,
});

export default router;
