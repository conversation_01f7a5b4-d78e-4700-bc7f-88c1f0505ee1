import { nextTick, createApp, h } from 'vue';
import { Tooltip } from 'ant-design-vue';

const style = {
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  maxWidth: '100%',
};

// 自定义指令：文字一行显示，超出时显示 tooltip
export const vEllipsisTooltip = {
  mounted(el, binding) {
    const { value } = binding;

    // 设置样式
    el.style.whiteSpace = 'nowrap';
    el.style.overflow = 'hidden';
    el.style.textOverflow = 'ellipsis';
    el.style.maxWidth = value?.maxWidth || '100%';

    // 检查是否需要 tooltip
    nextTick(() => {
      wrapWithTooltip(el, value);
    });
  },

  updated(el, binding) {
    const { value } = binding;

    // 更新时重新检查
    nextTick(() => {
      wrapWithTooltip(el, value);
    });
  },

  unmounted(el) {
    // 清理 tooltip 实例和容器
    if (el._tooltipInstance) {
      el._tooltipInstance.unmount();
      el._tooltipInstance = null;
    }
    if (el._tooltipContainer) {
      el._tooltipContainer.remove();
      el._tooltipContainer = null;
    }
  }
};

// 包装元素并设置 tooltip
function wrapWithTooltip(el, options) {
  const text = el.textContent || el.innerText;
  const isOverflow = el.scrollWidth > el.clientWidth;

  style.maxWidth = options?.maxWidth || '100%';

  // 如果已经存在 tooltip 实例，先移除
  if (el._tooltipInstance) {
    el._tooltipInstance.unmount();
    el._tooltipInstance = null;
  }

  if (isOverflow) {
    // 如果不存在 tooltipContainer，则创建并保存
    if (!el._tooltipContainer) {
      const tooltipContainer = document.createElement('div');
      tooltipContainer.style.display = 'inline-block'; // 确保容器是 inline-block，避免布局问题
      el._tooltipContainer = tooltipContainer;
      el.parentNode.insertBefore(tooltipContainer, el);
    }

    // 创建 tooltip 实例
    const tooltipApp = createApp({
      render() {
        return h(
          Tooltip,
          {
            placement: options?.placement || 'top',
            color: options?.color,
            overlayClassName: options?.overlayClassName,
          },
          {
            default: () => h('div', { class: el.className, style: style }, el.innerHTML), // 将原始内容放入 default 插槽
            title: () => h('span', {}, text),
          }
        );
      },
    });

    tooltipApp.mount(el._tooltipContainer);

    // 隐藏原始元素
    el.style.display = 'none';

    // 保存实例以便后续清理
    el._tooltipInstance = tooltipApp;
  } else {
    // 如果不溢出，显示原始内容并移除 tooltipContainer
    el.style.display = ''; // 确保原始元素可见
    if (el._tooltipContainer) {
      el._tooltipContainer.remove();
      el._tooltipContainer = null;
    }
  }
}

// 导出所有指令
export const directives = {
  'ellipsis-tooltip': vEllipsisTooltip,
};