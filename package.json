{"name": "intelligent-control-design", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode localtest", "dev": "vue-cli-service serve --mode dev", "build": "vue-cli-service build", "build:test": "vue-cli-service build --mode test", "build:prod": "vue-cli-service build --mode production", "build:pre": "vue-cli-service build --mode pre", "lint": "eslint  --fix", "prettier": "prettier --write \"src/**/*.{js,json,jsx,css,less,scss,vue,html,md}\"", "prepare": "husky install"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@syn/ant-design4-vue3": "^1.0.42", "@syn/syn-icons": "^1.0.32", "ali-oss": "^6.23.0", "axios": "^1.10.0", "core-js": "^3.8.3", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "swiper": "^11.2.10", "vue": "^3.2.13", "vue-router": "^4.5.1", "vue3-colorpicker": "^2.3.0"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.24.0", "@plugin-web-update-notice/webpack": "^2.0.2", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "compression-webpack-plugin": "^11.1.0", "eslint": "^8.57.1", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "9.32", "globals": "^16.3.0", "husky": "^8.0.0", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "stylus-loader": "^8.1.1", "vue-eslint-parser": "^10.2.0"}, "lint-staged": {"*.{vue,js}": "eslint --fix"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}